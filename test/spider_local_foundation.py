from urllib3 import disable_warnings
from resx.log import setup_logger
import os

logger = setup_logger(use_crawler_log=True, crawler_log_enable=False, name=__name__, debug=False)
from eventlog_spider.crawler.crawler_foundation import FoundationCrawler as Crawler
from eventlog_spider.parser.parser_foundation import FoundationParser as Parser
from eventlog_spider.common.eventlog import EventlogNew as Eventlog, SpiderCode

disable_warnings()
os.environ['POD_ENV'] = 'online'

eventlog = Eventlog.from_dict(
    {
        "event_id": "octopus_entry-npo-52360900556003530D-default-1703756336",
        "code": -1,
        "selector": {
            "send_ts": 1703756336,
            "receive_ts": -1,
            "reason": "schedule",
            "clue": False,
            "entry_name": "credit",
            "inst_name": "china_npo",
            # "word": "536501035802114016",
            "word": "53340000MJA540311Q",
            # "word": "上海市华侨事业发展基金会",
            "info": {
                # 'cid': 131129119,
            },
            "try_id": 0,
            "meta": {},
            "weight": 960
        },
        "spider": {
            "receive_ts": -1,
            "send_ts": -1,
            "spider_data": {
                # "page_ts": 1706264137
            }
        }
    }
)

crawler = Crawler()
parser = Parser()

crawler.crawl(eventlog)
parser.parse(eventlog)
logger.info(f'after parser {eventlog.model_dump_json()}')
