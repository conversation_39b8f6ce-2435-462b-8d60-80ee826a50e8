# -*- coding: UTF-8 -*-
"""
@Title   ：
<AUTHOR>
@Project ：eventlog-spider 
@File    ：msv_hk_and_lawyers_test.py
@Date    ：2025/5/23 13:42 
"""
from biz_utils.msv_write import msv_write_hk_company_base_info
from biz_utils.msv_write import msv_write_lawyers_info
from biz_utils.msv_write import MSVSource
from biz_utils.msv_read import msv_read_list
from resx.log import setup_logger

if __name__ == '__main__':
    logger = setup_logger()

    data = {
        "key1": "全维度测试有限责任公司1",
        "key2": "aaaa",
        "key3": "",
        "key4": ""
    }
    # ret = msv_write_hk_company_base_info(br_num="QQAAZZWWSSXXEEDDCC!@#", source=MSVSource.HK, item=data)
    # logger.info(ret)

    data = [
        {
            "license": "XXXX1",
            "key1": "全维度测试有限责任公司1",
            "key2": "aaaa",
            "key3": "",
            "key4": ""
        },
        {
            "license": "XXXX2",
            "key1": "全维度测试有限责任公司2",
            "key2": "aaaa",
            "key3": "",
            "key4": ""
        }
    ]
    # ret = msv_write_lawyers_info(credit_no="QQAAZZWWSSXXEEDDCC!@#", source=MSVSource.LAWYER_12348, items=data)
    # logger.info(ret)

    ret = msv_read_list(credit_no="31110000666250989D", table_name="law_lawyers_list", source=MSVSource.LAWYER_12348.value)
    logger.info(ret)

    # ret = msv_read_list(br_num="QQAAZZWWSSXXEEDDCC!@#", table_name="hk_company_base_info", source=MSVSource.HK.value)
    # logger.info(ret)
