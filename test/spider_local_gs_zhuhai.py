# encoding=utf8
from urllib3 import disable_warnings
from resx.log import setup_logger

logger = setup_logger(use_crawler_log=True, crawler_log_enable=False, name=__name__, debug=False)

from eventlog_spider.common.eventlog import EventlogOld, SpiderCode
from eventlog_spider.crawler.crawler_gs_zhuhai import <PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON>raw<PERSON>
from eventlog_spider.parser.parser_gs_zhuhai import ZhuhaiParser as Parser

disable_warnings()

eventlog = EventlogOld.from_dict(
    {"event_id": "octopus_entry-company-92857993-jx-1739183324", "is_clue": False, "spider_code": -1, "crawlerType": 1,
     "crawlerCode": -1, "parserCode": -1, "fusionCode": -1,
     "selector": {"send_ts": 1739183324, "receive_ts": -1, "reason": "schedule", "item_name": "company",
                  "inst_name": "jx",
                  "word": "",
                  "info": {
                      "keyword": "91440402MAE1GW34X2",
                  }, "try_id": 0, "meta": None,
                  "weight": 300}, "spider": {"receive_ts": -1, "send_ts": -1, "item_insert": False, "ab_info": {}},
     "crawler": {}, "parser": {}, "fusion": {}, "dims": {}, "channel": {}}
)
crawler = Crawler()
parser = Parser()

crawler.crawl(eventlog)
parser.parse(eventlog)
logger.info(f'after parser {eventlog.model_dump_json()}')
