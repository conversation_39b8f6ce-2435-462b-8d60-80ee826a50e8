import json
import time
import requests
from urllib3 import disable_warnings
from resx.log import setup_logger
from resx.mysql_client import MySQLClient
from resx.config import *
from requests.adapters import HTTPAdapter
import os
from datetime import datetime
from pydantic import Field, conint
from typing import Optional, TypeVar, Dict
import random

from resx.base_model import BaseModel
from eventlog_spider.crawler.crawler_gds import GdsCrawler as Crawler
from eventlog_spider.crawler.crawler import CrawlerTools, MyException
from resx.redis_types import Redis
from resx.kafka_client import KafkaProducerClient
from resx.config import *
from resx.ext.mysql_rw_splitting import MySQLRWSplittingDao

logger = setup_logger(use_crawler_log=True, crawler_log_enable=False, name=__name__, debug=False)
disable_warnings()
os.environ['POD_ENV'] = 'online'
crawler = Crawler()
redis = Redis(**CFG_REDIS_GS, db=9)
fusion_producer_client = KafkaProducerClient(bootstrap_servers='kafka.middleware.huawei:9092', kafka_topic='basic_crawler_feedback')
product_dao = MySQLRWSplittingDao(
    mysql_ro=CFG_MYSQL_GS_INNER,
    db_tb_name='prism.gds_product',
    primary_index_fields=(['barcode'], []),
    # entity_class=ProductEntity,
    dim='',
    ignore_fields=['id', 'create_time']
)


class CrawlerTask(object):
    def __init__(self):
        self.session = requests.session()
        self.session.proxies = CrawlerTools.get_long_proxy()
        self.session.mount('http://', HTTPAdapter(max_retries=2))
        self.session.mount('https://', HTTPAdapter(max_retries=2))


while True:
    task: list = redis.zpopmin('gds_barcode')
    task_ = CrawlerTask()
    for event in task:
        eventlog = json.loads(event[0])
        try:
            if crawler.remove_403(task_.session):
                crawler.login()

            aa: dict = crawler.search(task_, eventlog['selector']['info']['barcode'])

            # 注册
            url_ = "https://bff.gds.org.cn/gds/searching-api/ProductService/ProductSimpleInfoByGTIN"
            response = task_.session.get(url_, params={"gtin": aa['gtin'], "id": aa['base_id']}, headers=crawler.headers, timeout=5)
            time.sleep(random.randint(1, 2))

            products_info = crawler.get_product_info(task_, {'gtin': aa['gtin'], 'base_id': aa['base_id']})
            if not products_info:
                raise MyException('11')
            products_info = products_info[0]['ProductDetailsViewInfoNationalList'][0]
            products_info.update(aa)
            print(products_info)
        except MyException as e:
            if e.message in ['11', '国外或没有barcode']:
                eventlog['code'] = 11
            else:
                eventlog['code'] = 2
        except Exception as e:
            eventlog['code'] = 2

        if eventlog['code'] == -1:
            barcode = products_info['GTIN']
            NetContent = products_info['NetContent'] if products_info['NetContent'] else ''
            NetContentUnitofMeasureDescription = products_info['NetContentUnitofMeasureDescription'] if products_info[
                'NetContentUnitofMeasureDescription'] else ""

            pp = {
                'firm_id': int(products_info.get('FirmID', 0)),
                'brand_id': int(products_info.get('BrandID', 0)),
                'product_id': int(products_info.get('ID', 0)),
                'product_desc': products_info.get('ProductDescription', '').replace(' ', ''),
                'product_img_paths': '',
                'barcode': barcode,
                'barcode_img_path': products_info.get('barcode_pic', ''),
                'barcode_status': products_info.get('gtinstatus', 0),
                'gpc_code': products_info.get('GlobalProductCategoryCode', '') or '',
                'gpc_name': products_info.get('GlobalProductCategoryName', '') or '',
                'first_ship_date': datetime.strptime(products_info['FirstShipDateTime'][:10], '%Y-%m-%d') if
                products_info['FirstShipDateTime'] else datetime.strptime('0001-01-01', '%Y-%m-%d'),
                'product_spec': NetContent + NetContentUnitofMeasureDescription,
                'product_net_content': products_info.get('NetContentStatement', '') or '',
                'crawl_status': 0
            }
            product_dao.save(pp)
            eventlog['code'] = 0
            fusion_producer_client.write(json.dumps(eventlog, ensure_ascii=False))
        else:
            fusion_producer_client.write(json.dumps(eventlog, ensure_ascii=False))
    logger.info('Finished processing task, waiting for next task...')
    time.sleep(1)
