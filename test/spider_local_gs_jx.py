# encoding=utf8

from urllib3 import disable_warnings
# from resx.log import setup_logger
from crawler_log.log_init_v2 import Log
logger = Log('eventlog-crawler', env='other', domain='eventlog-crawler', dimension='eventlog-crawler')

from eventlog_spider.common.eventlog import EventlogOld, SpiderCode
from eventlog_spider.crawler.crawler_gs_jx import GSJ<PERSON>Crawler as Crawler
from eventlog_spider.parser.parser_gs_jx import GSJXParser as Parser

# logger = setup_logger()
disable_warnings()

# 复制此代码构建自己的本地测试代码，不用提交git


eventlog = EventlogOld.from_dict(
    {"event_id": "octopus_entry-company-92857993-jx-1739183324", "is_clue": False, "spider_code": -1, "crawlerType": 1,
     "crawlerCode": -1, "parserCode": -1, "fusionCode": -1,
     "selector": {"send_ts": 1739183324, "receive_ts": -1, "reason": "schedule", "item_name": "company",
                  "inst_name": "jx", "word": "*********",
                  "info": {"keyword": "91360106MA37N3QR92",
                       }, "try_id": 0, "meta": None,
                  "weight": 300}, "spider": {"receive_ts": -1, "send_ts": -1, "item_insert": False, "ab_info": {}},
     "crawler": {}, "parser": {}, "fusion": {}, "dims": {}, "channel": {}}
)
crawler = Crawler()
parser = Parser()

crawler.crawl(eventlog)
parser.parse(eventlog)
logger.info(f'after parser {eventlog.model_dump_json()}')
