from resx.log import setup_logger
import re
import time
import os

logger = setup_logger(use_crawler_log=True, crawler_log_enable=False, name=__name__, debug=False)
from eventlog_spider.crawler.crawler_gov_unit import GovUnitCrawler as Crawler
from eventlog_spider.parser.parser_gov_unit import GovUnitParser as Parser
from eventlog_spider.common.eventlog import EventlogNew as Eventlog, SpiderCode

os.environ['POD_ENV'] = 'online'

crawler = Crawler()
parser = Parser()


def get_eventlog(name):
    return Eventlog.from_dict(
        {
            "event_id": f"octopus_entry-cods-52360900556003530D-default-{int(time.time())}",
            "spider_code": -1,
            "selector": {
                "send_ts": 1703756336,
                "receive_ts": -1,
                "reason": "schedule",
                "clue": True,
                "entry_name": "credit",
                "inst_name": "china_npo",
                # "word": "11441521007247428E",
                "word": name,
                "info": {
                    # 'cid': 131129119,
                },
                "try_id": 0,
                "meta": {},
                "weight": 960
            },
            "spider": {
                "receive_ts": -1,
                "send_ts": -1,
                "spider_data": {
                    # "page_ts": 1706264137
                }
            }
        }
    )


for i in ('12513331078860991J',):
    # if not re.search(r'^12.{16}$', i):
    #     print(i)
    #     continue
    a = get_eventlog(i)
    crawler.crawl(a)
    parser.parse(a)
    logger.info(f'after parser {a.model_dump_json()}')
