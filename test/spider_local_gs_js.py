# encoding=utf8
from urllib3 import disable_warnings
from crawler_log.log_init_v2 import Log
logger = Log('eventlog-crawler', env='other', domain='eventlog-crawler', dimension='eventlog-crawler')
from eventlog_spider.common.eventlog import EventlogOld, SpiderCode
from eventlog_spider.crawler.crawler_gs_js import GSJSCrawler as Crawler
from eventlog_spider.parser.parser_gs_js import GSJSParser as Parser

# logger = setup_logger()
disable_warnings()

# 复制此代码构建自己的本地测试代码，不用提交git


eventlog = EventlogOld.from_dict(
    {"event_id": "octopus_entry-company-90559932-js-1739002588", "is_clue": False, "spider_code": -1, "crawlerType": 1,
     "crawlerCode": -1, "parserCode": -1, "fusionCode": -1,
     "selector": {"send_ts": 1739002588, "receive_ts": -1, "reason": "schedule", "item_name": "company",
                  "inst_name": "js", "word": "68344127",
                  "info": {"name": "扬州阿牛观光农业有限公司", "credit_code": "91321023MA1MF4K84P",
                           "reg_number": "320508000553939", "reg_status": "存续（在营、开业、在册）",
                           "establish_date": "2018-05-25", "org_type": "有限责任公司(自然人投资或控股的法人独资)",
                           "reg_institute": "连云港市海州区行政审批局", "realtime_ts": 1738591130,
                           "keyword": "91321023MA1MF4K84P"}, "try_id": 0, "meta": None, "weight": 400},
     "spider": {"receive_ts": -1, "send_ts": -1, "item_insert": False, "ab_info": {}}, "crawler": {}, "parser": {},
     "fusion": {}, "dims": {}, "channel": {}}
)

crawler = Crawler()
parser = Parser()

crawler.crawl(eventlog)
parser.parse(eventlog)
# logger.info(f'after parser {eventlog.model_dump_json()}')
