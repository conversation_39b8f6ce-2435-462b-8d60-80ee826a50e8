# -*- coding: UTF-8 -*-
"""
@Title   ：
<AUTHOR>
@Project ：eventlog-spider 
@File    ：msv_non_ic_test.py
@Date    ：2025/5/20 17:29 
"""
from biz_utils.msv_write import msv_write_non_ic_company_base_info
from biz_utils.msv_read import msv_read_list
from biz_utils.msv_write import MSVSource
from resx.log import setup_logger


if __name__ == '__main__':
    # 读取
    logger = setup_logger()
    ret = msv_read_list(credit_no="52100000MJ00600QWD", table_name="non_ic_company_base_info")
    logger.info(ret)

    # 写入
    data = {
        "test_key1": "value1",
        "test_key2": "value1",
    }
    ret = msv_write_non_ic_company_base_info(credit_no="52100000MJ00600QWD", source=MSVSource.NPO_GJ, item=data)
    logger.info(ret)

