import time
from urllib3 import disable_warnings
from resx.log import setup_logger
from resx.mysql_client import MySQLClient
from resx.config import *
import re
import os
import json

logger = setup_logger(use_crawler_log=True, crawler_log_enable=False, name=__name__, debug=False)
from eventlog_spider.crawler.crawler_tw import Tw<PERSON>rawler as Crawler
from eventlog_spider.parser.parser_tw import Tw<PERSON>arser as Parser
from eventlog_spider.common.eventlog import EventlogNew as Eventlog, SpiderCode
from eventlog_spider.common.eventlog_unify import Eventlog as UnifyEventlog, StatusCode
from resx.redis_types import Redis
from resx.kafka_client import KafkaProducerClient
from eventlog_spider.crawler.crawler import CrawlerTools, MyException

disable_warnings()
os.environ['POD_ENV'] = 'online'
redis = Redis(**CFG_REDIS_GS, db=9)
fusion_producer_client = KafkaProducerClient(bootstrap_servers='kafka.middleware.huawei:9092', kafka_topic='basic_crawler_feedback')
crawler = Crawler()
parser = Parser()


def get_eventlog(name):
    return Eventlog.from_dict(
        {
            "event_id": f"octopus_entry-cods-52360900556003530D-default-{int(time.time())}",
            "spider_code": -1,
            "selector": {
                "send_ts": 1703756336,
                "receive_ts": -1,
                "reason": "schedule",
                "clue": True,
                "entry_name": "credit",
                "inst_name": "tw",
                # "word": "11441521007247428E",
                "word": name,
                "info": {
                    # 'cid': 131129119,
                },
                "try_id": 0,
                "meta": {},
                "weight": 960
            },
            "spider": {
                "receive_ts": -1,
                "send_ts": -1,
                "spider_data": {
                    # "page_ts": 1706264137
                }
            }
        }
    )


while True:
    # task: list = redis.zpopmin('tw', count=1)
    task = ['']
    for event in task:
        # eventlog_: UnifyEventlog = UnifyEventlog.from_dict(json.loads(event[0]))
        try:
            # eventlog = get_eventlog(eventlog_.selector.info['code'])
            eventlog = get_eventlog('84894989')
            crawler.crawl(eventlog)
            parser.parse(eventlog)
        except MyException as e:
            pass
            # eventlog_.code = e.message
        except Exception as e:
            logger.error(CrawlerTools.custom_traceback(e))
            # eventlog_.code = StatusCode.GIVE_UP

        # if eventlog_.code == -1:
        #     eventlog_.code = StatusCode.SUCCESS
        #     fusion_producer_client.write(json.dumps(eventlog_.model_dump(), ensure_ascii=False))
        # else:
        #     fusion_producer_client.write(json.dumps(eventlog_.model_dump(), ensure_ascii=False))
    logger.info('Finished processing task, waiting for next task...')
    time.sleep(1)
