from urllib3 import disable_warnings
from crawler_log.log_init_v2 import Log
logger = Log('eventlog-crawler', env='other', domain='eventlog-crawler', dimension='eventlog-crawler')
from eventlog_spider.common.eventlog import EventlogOld, SpiderCode
from eventlog_spider.crawler.crawler_gs_guangdong import GSGuangDongCrawler as Crawler
from eventlog_spider.parser.parser_gs_guangdong import GSGuangDongParser as Parser

disable_warnings()

log = {
    "event_id": "octopus_entry-company-1457357-gd-1740980088",
    "is_clue": False,
    "spider_code": -1,
    "crawlerType": 1,
    "crawlerCode": -1,
    "parserCode": -1,
    "fusionCode": -1,
    "selector": {
        "send_ts": 1740980088,
        "receive_ts": -1,
        "reason": "platform",
        "item_name": "company",
        "inst_name": "gd",
        "word": "2704341353",
        "info": {
            "name": "梅州市灿路广告业工作室（个人独资）",
            "credit_code": "91441481MADW59626L",
            "reg_number": "***************",
            "reg_status": "存续（在营、开业、在册）",
            "establish_date": "2002-12-18",
            "org_type": None,
            "reg_institute": "中山市市场监督管理局",
            "important_type": 162,
            "keyword": "91441481MADW59626L",
            "credit_cde": "91442000746254031K",
            "realtime_ts": 1740980084,
            "report_2023_check_ts": -1
        },
        "try_id": 0,
        "meta": None,
        "weight": 920
    },
    "spider": {
        "receive_ts": -1,
        "send_ts": -1,
        "item_insert": False,
        "ab_info": {}
    },
    "crawler": {
        "crawlerInfo": {
            "detailUrl": "https://gsxt.amr.gd.gov.cn/#/ent/detail?key=eyJlbnRObyI6IjNkZDg5NDhlLTgzMmYtNDdhYS1iMzY2LWQ4YzVhYThmNDg2YSIsInJlZ09yZyI6IjQ0MjAwMCIsImVudFR5cGUiOiIxMTMwIn0%3D"
        }
    },
    "parser": {},
    "fusion": {},
    "dims": {
        "copyright": {
            "crawlerCode": -1,
            "fusionCode": -1,
            "dispatcherCode": 201,
            "parserCode": -1
        },
        "insPunish": {
            "crawlerCode": -1,
            "fusionCode": -1,
            "dispatcherCode": 201,
            "parserCode": -1
        },
        "elicenseNullfy": {
            "crawlerCode": -1,
            "fusionCode": -1,
            "dispatcherCode": 201,
            "parserCode": -1
        },
        "eselfinfo": {
            "crawlerCode": -1,
            "fusionCode": -1,
            "dispatcherCode": 201,
            "parserCode": -1
        },
        "supervisionCheckres": {
            "crawlerCode": -1,
            "fusionCode": -1,
            "dispatcherCode": 201,
            "parserCode": -1
        },
        "branch": {
            "crawlerCode": -1,
            "fusionCode": -1,
            "dispatcherCode": 201,
            "parserCode": -1
        },
        "instant": {
            "crawlerCode": -1,
            "fusionCode": -1,
            "dispatcherCode": 201,
            "parserCode": -1
        },
        "keyperson": {
            "crawlerCode": -1,
            "fusionCode": -1,
            "dispatcherCode": 201,
            "parserCode": -1
        },
        "punish": {
            "crawlerCode": -1,
            "fusionCode": -1,
            "dispatcherCode": 201,
            "parserCode": -1
        },
        "insAltStock": {
            "crawlerCode": -1,
            "fusionCode": -1,
            "dispatcherCode": 201,
            "parserCode": -1
        },
        "trademark": {
            "crawlerCode": -1,
            "fusionCode": -1,
            "dispatcherCode": 201,
            "parserCode": -1
        },
        "stock": {
            "crawlerCode": -1,
            "fusionCode": -1,
            "dispatcherCode": 201,
            "parserCode": -1
        },
        "ePubGroupMenberInfo": {
            "crawlerCode": -1,
            "fusionCode": -1,
            "dispatcherCode": 201,
            "parserCode": -1
        },
        "alter": {
            "crawlerCode": -1,
            "fusionCode": -1,
            "dispatcherCode": 201,
            "parserCode": -1
        },
        "eNliq": {
            "crawlerCode": -1,
            "fusionCode": -1,
            "dispatcherCode": 201,
            "parserCode": -1
        },
        "neRecItem": {
            "crawlerCode": -1,
            "fusionCode": -1,
            "dispatcherCode": 201,
            "parserCode": -1
        },
        "clear": {
            "crawlerCode": -1,
            "fusionCode": -1,
            "dispatcherCode": 201,
            "parserCode": -1
        },
        "ill": {
            "crawlerCode": -1,
            "fusionCode": -1,
            "dispatcherCode": 201,
            "parserCode": -1
        },
        "shareholder": {
            "crawlerCode": -1,
            "fusionCode": -1,
            "dispatcherCode": 201,
            "parserCode": -1
        },
        "check": {
            "crawlerCode": -1,
            "fusionCode": -1,
            "dispatcherCode": 201,
            "parserCode": -1
        },
        "nlic": {
            "crawlerCode": -1,
            "fusionCode": -1,
            "dispatcherCode": 201,
            "parserCode": -1
        },
        "simplecancer": {
            "crawlerCode": -1,
            "fusionCode": -1,
            "dispatcherCode": 201,
            "parserCode": -1
        },
        "insnlic": {
            "crawlerCode": -1,
            "fusionCode": -1,
            "dispatcherCode": 201,
            "parserCode": -1
        },
        "proquacheck": {
            "crawlerCode": -1,
            "fusionCode": -1,
            "dispatcherCode": 201,
            "parserCode": -1
        },
        "susnate": {
            "crawlerCode": -1,
            "fusionCode": -1,
            "dispatcherCode": 201,
            "parserCode": -1
        },
        "foodChkInfo": {
            "crawlerCode": -1,
            "fusionCode": -1,
            "dispatcherCode": 201,
            "parserCode": -1
        },
        "abnormal": {
            "crawlerCode": -1,
            "fusionCode": -1,
            "dispatcherCode": 201,
            "parserCode": -1
        },
        "drranins": {
            "crawlerCode": -1,
            "fusionCode": -1,
            "dispatcherCode": 201,
            "parserCode": -1
        },
        "assist": {
            "crawlerCode": -1,
            "fusionCode": -1,
            "dispatcherCode": 201,
            "parserCode": -1
        },
        "annualreport": {
            "crawlerCode": -1,
            "fusionCode": -1,
            "dispatcherCode": 201,
            "parserCode": -1
        },
        "base": {
            "crawlerCode": -1,
            "fusionCode": -1,
            "dispatcherCode": 201,
            "parserCode": -1
        }
    },
    "channel": {}
}

# aaa = "2229088005	小饿狼电器股份有限公司	91440101MA9W1UQEX1"
# company_id = aaa.split("	")[0]
# name = aaa.split("	")[1]
# key_word = aaa.split("	")[2]
#
# log["selector"]["word"] = company_id
# log["selector"]["info"]["name"] = name
# log["selector"]["info"]["credit_code"] = key_word
# log["selector"]["info"]["keyword"] = key_word
# log["selector"]["info"]["credit_cde"] = key_word

eventlog: EventlogOld = EventlogOld.from_dict(log)
crawler = Crawler()
parser = Parser()

crawler.crawl(eventlog)
logger.info(f'crawler done {eventlog.to_json()}')
parser.parse(eventlog)
logger.info(f'parser done {eventlog.to_json()}')
