import time
import re
from loguru import logger
from urllib3 import disable_warnings

from resx.mysql_dao import MySQLDao
from resx.config import *
from eventlog_spider.common.eventlog import EventlogOld, SpiderCode
from eventlog_spider.crawler.crawler_gs_zhejiang import <PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON>rawler
from eventlog_spider.parser.parser_gs_zhejiang import <PERSON><PERSON><PERSON><PERSON> as Parser
from resx.kafka_client import KafkaProducerClient
from eventlog_spider.crawler.crawler import CrawlerTools

# from resx.log import setup_logger

task_dao = MySQLDao(**CFG_MYSQL_GS_INNER, db_tb_name='internal.platform_task')
fusion_producer_client = KafkaProducerClient(bootstrap_servers='kafka.middleware.huawei:9092', kafka_topic='gsxt.data_fusion')
disable_warnings()


# logger = setup_logger(logger_path='./logs/zhejiang.log', backup_count=10, name=__name__)


def get_eventlog(keyword):
    return EventlogOld.from_dict(
        {
            "event_id": "octopus_entry-company-92857993-zj-1739183324",
            "is_clue": False,
            "spider_code": -1,
            "crawlerType": 1,
            "crawlerCode": -1,
            "parserCode": -1,
            "fusionCode": -1,
            "selector": {
                "send_ts": 1739183324,
                "receive_ts": -1,
                "reason": "schedule",
                "item_name": "company",
                "inst_name": "zj",
                "word": "",
                "info": {
                    "keyword": keyword
                },
                "try_id": 0,
                "meta": None,
                "weight": 300
            },
            "spider": {
                "receive_ts": -1,
                "send_ts": -1,
                "item_insert": False,
                "ab_info": {}
            },
            "crawler": {},
            "parser": {},
            "fusion": {},
            "dims": {},
            "channel": {}
        }
    )


while True:
    tasks = list(task_dao.select_many("select * from internal.platform_task where task_type = 74 and status = '创建' limit 10"))
    # tasks = [{'id': 3152924,
    #           'param': 'https://gswsdj.zjzwfw.gov.cn/daprint.do?method=goJbxx&csrftoken=71716726&pripid=91330304MAEMKJDN4F&uniscid=913301067046373179&sign=c2e108b3efaf2c2bf5c013493776a8338614519672b821c7ec24b62a07ac6e7f'}]
    for i in tasks:
        try:
            url = i['param']
            uniscid = re.search(r'&uniscid=(.*?)&', url).group(1)
            eventlog = get_eventlog(uniscid)
            crawler = Crawler()
            parser = Parser()

            crawler.crawl(eventlog)
            parser.parse(eventlog)
            logger.info(f'after parser {eventlog.model_dump_json()}')
            if eventlog.spider_code == SpiderCode.SUCCESS:
                eventlog.spider.send_ts = int(time.time())
                eventlog_str = eventlog.to_json()
                ret = fusion_producer_client.write(eventlog_str)
                task_dao.execute(f'update internal.platform_task set status = "成功" where id = {i["id"]}')
            else:
                task_dao.execute(f'update internal.platform_task set status = "失败" where id = {i["id"]}')
        except Exception as e:
            error = CrawlerTools.custom_traceback(e)
            logger.error(f'Error processing task {i["id"]}: {error}')
            task_dao.execute(f'update internal.platform_task set status = "失败" where id = {i["id"]}')

    logger.info('Sleeping for 2 seconds before next iteration...')
    time.sleep(2)
