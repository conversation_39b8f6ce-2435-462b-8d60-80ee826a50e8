from urllib3 import disable_warnings
from crawler_log.log_init_v2 import Log
import logging
import os
from resx.log import setup_logger
import re

os.environ['POD_ENV'] = 'online'

# logger = Log('eventlog-crawler', env='other', domain='eventlog-crawler', dimension='eventlog-crawler', level=logging.INFO)
logger = setup_logger(use_crawler_log=True, crawler_log_enable=False, name=__name__, debug=False)

from eventlog_spider.crawler.crawler_law import Crawler<PERSON><PERSON> as Crawler
from eventlog_spider.parser.parser_law import <PERSON><PERSON><PERSON><PERSON><PERSON> as Parser
from eventlog_spider.common.eventlog import EventlogNew as Eventlog, SpiderCode


def get_eventlog(word):
    return Eventlog.from_dict(
        {
            "event_id": "octopus_entry-npo-52360900556003530D-default-1703756336",
            "spider_code": -1,
            "selector": {
                "send_ts": 1703756336,
                "receive_ts": -1,
                "reason": "schedule",
                "clue": False,
                "entry_name": "credit",
                "inst_name": "law_firm_gj",
                "word": word,
                # "word": "31110000MD039449X2",
                "info": {
                    # 'cid': 131129119,
                },
                "try_id": 0,
                "meta": {},
                "weight": 960
            },
            "spider": {
                "receive_ts": -1,
                "send_ts": -1,
                "spider_data": {
                    # "page_ts": 1706264137
                }
            }
        }
    )


crawler = Crawler()
parser = Parser()

for i in ['31370000MD03704638']:
    eventlog = get_eventlog(i)
    crawler.crawl(eventlog)
    parser.parse(eventlog)
