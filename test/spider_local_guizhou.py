# -*- coding: UTF-8 -*-
"""
@Project ：eventlog-spider 
@File    ：spider_local_guizhou.py
<AUTHOR>
@Date    ：2025/3/3 15:47 
"""
from urllib3 import disable_warnings
# from resx.log import setup_logger
from resx.log import setup_logger
logger = setup_logger(use_crawler_log=True, crawler_log_enable=False, name=__name__, debug=False)

from eventlog_spider.common.eventlog import EventlogOld, SpiderCode
from eventlog_spider.crawler.crawler_gs_guizhou import GSGuiZhouCrawler as Crawler
from eventlog_spider.parser.parser_gs_guizhou import G<PERSON><PERSON>uiZhouParser as Parser

# logger = setup_logger()
disable_warnings()

log = {
    "event_id": "octopus_entry-company-88249272-gz-1740988081",
    "is_clue": False,
    "spider_code": -1,
    "crawlerType": 1,
    "crawlerCode": -1,
    "parserCode": -1,
    "fusionCode": -1,
    "selector": {
        "send_ts": 1740988081,
        "receive_ts": -1,
        "reason": "schedule",
        "item_name": "company",
        "inst_name": "gz",
        "word": "*********",
        "info": {
            "name": "惠水县佳仲建筑设备租赁有限责任公司",
            "credit_code": "91522731MA6H247932",
            "reg_number": "522731000262696",
            "reg_status": "存续（在营、开业、在册）",
            "establish_date": "2018-06-12",
            "org_type": "有限责任公司(自然人投资或控股)",
            "reg_institute": "黔南州惠水县市场监督管理局",
            "realtime_ts": 1739007506,
            "keyword": "91522731MA6H247932",
            "report_2022_miss_social_ts": -1,
            "report_2023_check_ts": -1
        },
        "try_id": 0,
        "meta": None,
        "weight": 250
    },
    "spider": {
        "receive_ts": -1,
        "send_ts": -1,
        "item_insert": False,
        "ab_info": {}
    },
    "crawler": {},
    "parser": {},
    "fusion": {},
    "dims": {},
    "channel": {}
}

aaa = "*********	贵州国台数智酒业集团股份有限公司	91520382722183654F"
company_id = aaa.split("	")[0]
name = aaa.split("	")[1]
key_word = aaa.split("	")[2]

from loguru import logger
logger.warning(f"{company_id}, {name}, {key_word}")
log["selector"]["word"] = company_id
log["selector"]["info"]["name"] = name
log["selector"]["info"]["credit_code"] = key_word
log["selector"]["info"]["keyword"] = key_word
log["selector"]["info"]["credit_cde"] = key_word

event_log = EventlogOld.from_dict(log)
crawler = Crawler()
parser = Parser()
crawler.crawl(event_log)
parser.parse(event_log)
