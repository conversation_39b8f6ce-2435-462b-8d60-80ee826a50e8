import time
from urllib3 import disable_warnings
from resx.log import setup_logger
from resx.mysql_client import MySQLClient
from resx.config import *
import re
import os

logger = setup_logger(use_crawler_log=True, crawler_log_enable=False, name=__name__, debug=False)
from eventlog_spider.crawler.crawler_cods import CodsCrawler as Crawler
from eventlog_spider.parser.parser_cods import CodsParser as Parser
from eventlog_spider.common.eventlog import EventlogNew as Eventlog, SpiderCode

disable_warnings()
mysql = MySQLClient(**CFG_MYSQL_GS_OUTER)
history_name_dao = MySQLClient(**CFG_MYSQL_GS_OUTER)

os.environ['POD_ENV'] = 'online'


def get_eventlog(name):
    return Eventlog.from_dict(
        {
            "event_id": f"octopus_entry-cods-52360900556003530D-default-{int(time.time())}",
            "spider_code": -1,
            "selector": {
                "send_ts": 1703756336,
                "receive_ts": -1,
                "reason": "schedule",
                "clue": True,
                "entry_name": "credit",
                "inst_name": "china_npo",
                # "word": "11441521007247428E",
                "word": name,
                "info": {
                    # 'cid': 131129119,
                },
                "try_id": 0,
                "meta": {},
                "weight": 960
            },
            "spider": {
                "receive_ts": -1,
                "send_ts": -1,
                "spider_data": {
                    # "page_ts": 1706264137
                }
            }
        }
    )


crawler = Crawler()
parser = Parser()

list_ = ['55500103MEA293164F']
temp = []
for i in list_:
    # if i.startswith('11'):
    #     temp.append(i)
    #     continue
    a = get_eventlog(i)
    crawler.crawl(a)
    parser.parse(a)
    logger.info(f'after parser {a.model_dump_json()}')
print(temp)
