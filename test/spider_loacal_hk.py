from resx.log import setup_logger
import os

logger = setup_logger(use_crawler_log=True, crawler_log_enable=False, name=__name__, debug=False)
from eventlog_spider.crawler.crawler_hk import <PERSON>rawlerH<PERSON> as Crawler
from eventlog_spider.parser.parser_hk import <PERSON><PERSON><PERSON><PERSON><PERSON> as Parser
from eventlog_spider.common.eventlog import EventlogNew as Eventlog, SpiderCode

os.environ['POD_ENV'] = 'online'


def get_eventlog(word):
    return Eventlog.from_dict({"event_id": "octopvs-brno-hk-39701029-1739850315", "code": -1,
                               "selector": {"send_ts": 1739850315, "receive_ts": 1739850562, "reason": "schedule", "clue": False, "entry_name": "brno",
                                            "inst_name": "hk", "word": word,
                                            "info": {"company_num": "1266159", "name": "丽川(香港)投资有限公司", "establish_date": "2008-08-19",
                                                     "status": "仍注册"}, "try_id": 0, "meta": {}, "weight": 104},
                               "spider": {"receive_ts": 1739850553, "send_ts": 1739850562, "spider_data": {"page_ts": 1739850557}, "ab_info": {}}})

crawler = Crawler()
parser = Parser()

for i in ('69830523',):
    eventlog = get_eventlog(i)

    crawler.crawl(eventlog)
    parser.parse(eventlog)
    logger.info(f'after parser {eventlog.model_dump_json()}')
