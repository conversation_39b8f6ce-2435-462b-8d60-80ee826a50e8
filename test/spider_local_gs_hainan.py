from urllib3 import disable_warnings
# from resx.log import setup_logger
from crawler_log.log_init_v2 import Log
logger = Log('eventlog-crawler', env='other', domain='eventlog-crawler', dimension='eventlog-crawler')
from eventlog_spider.common.eventlog import EventlogOld, SpiderCode
from eventlog_spider.crawler.crawler_gs_hainan import GSHaiNanCrawler as Crawler
from eventlog_spider.parser.parser_gs_hainan import GSHaiNanParser as Parser

# logger = setup_logger()
disable_warnings()

# 复制此代码构建自己的本地测试代码，不用提交git

log = {
    "event_id": "octopus_entry-company-82321808-han-1739172666",
    "is_clue": False,
    "spider_code": -1,
    "crawlerType": 1,
    "crawlerCode": -1,
    "parserCode": -1,
    "fusionCode": -1,
    "selector": {
        "send_ts": 1739172666,
        "receive_ts": -1,
        "reason": "schedule",
        "item_name": "company",
        "inst_name": "han",
        "word": "39751141",
        "info": {
            "name": "海南恒乾材料设备有限公司",
            "credit_code": "91460100MA5RC4PD2Y",
            "reg_number": "***************",
            "reg_status": "存续（在营、开业、在册）",
            "establish_date": "2018-03-22",
            "org_type": "有限责任公司(自然人独资)",
            "reg_institute": "海口市市场监督管理局",
            "keyword": "91460100MA5RC4PD2Y",
            "credit_cde": "91460100MA5RC4PD2Y",
            "realtime_ts": 1738620867
        },
        "try_id": 0,
        "meta": None,
        "weight": 200
    },
    "spider": {
        "receive_ts": -1,
        "send_ts": -1,
        "item_insert": False,
        "ab_info": {}
    },
    "crawler": {},
    "parser": {},
    "fusion": {},
    "dims": {},
    "channel": {}
}

# aaa = "13035562	白沙黎族自治县邦溪供销社阜龙供销农贸市场	91469030MA5RG9061X"
# company_id = aaa.split("	")[0]
# name = aaa.split("	")[1]
# key_word = aaa.split("	")[2]
#
#
# log["selector"]["word"] = company_id
# log["selector"]["info"]["name"] = name
# log["selector"]["info"]["credit_code"] = key_word
# log["selector"]["info"]["keyword"] = key_word
# log["selector"]["info"]["credit_cde"] = key_word

eventlog = EventlogOld.from_dict(log)
crawler = Crawler()
parser = Parser()

crawler.crawl(eventlog)
parser.parse(eventlog)