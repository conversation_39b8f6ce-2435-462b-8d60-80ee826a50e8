# -*- coding: UTF-8 -*-
"""
@Project ：pygs-work-parent
@File    ：gs_spider_heilongjiang_test.py
<AUTHOR>
@Date    ：2025/2/8 09:56
"""
from urllib3 import disable_warnings
# from resx.log import setup_logger
from crawler_log.log_init_v2 import Log
logger = Log('eventlog-crawler', env='other', domain='eventlog-crawler', dimension='eventlog-crawler')

from eventlog_spider.common.eventlog import EventlogOld, SpiderCode
from eventlog_spider.crawler.crawler_gs_heilongjiang import GSHeiLongJiangCrawler as Crawler
from eventlog_spider.parser.parser_gs_heilongjiang import GSHeiLongJiangParser as Parser

# logger = setup_logger()
disable_warnings()

log = {
    "event_id": "octopus_entry-company-98792776-hlj-1738980278",
    "is_clue": False,
    "spider_code": -1,
    "crawlerType": 1,
    "crawlerCode": -1,
    "parserCode": -1,
    "fusionCode": -1,
    "selector": {
        "send_ts": 1738980278,
        "receive_ts": -1,
        "reason": "schedule",
        "item_name": "company",
        "inst_name": "hlj",
        "word": "*********",
        "info": {
            "name": "哈尔滨产伍科技有限公司",
            "credit_code": "91230102MA1BEPB54P",
            "reg_number": "230102101270204",
            "reg_status": "存续（在营、开业、在册）",
            "establish_date": "2019-01-09",
            "org_type": "有限责任公司(自然人投资或控股)",
            "reg_institute": "哈尔滨市道里区市场监督管理局",
            "keyword": "91230102MA1BEPB54P",
            "credit_cde": "91230102MA1BEPB54P",
            "realtime_ts": 1735772814,
            "report_2022_check_ts": 1692875308
        },
        "try_id": 0,
        "meta": None,
        "weight": 300
    },
    "spider": {
        "receive_ts": -1,
        "send_ts": -1,
        "item_insert": False,
        "ab_info": {}
    },
    "crawler": {},
    "parser": {},
    "fusion": {},
    "dims": {},
    "channel": {}
}

aaa = "72234922	黑龙江华龙酒直达供应链管理股份有限公司	91230199578062574M"
company_id = aaa.split("	")[0]
name = aaa.split("	")[1]
key_word = aaa.split("	")[2]

from loguru import logger
logger.warning(f"{company_id}, {name}, {key_word}")
log["selector"]["word"] = company_id
log["selector"]["info"]["name"] = name
log["selector"]["info"]["credit_code"] = key_word
log["selector"]["info"]["keyword"] = key_word
log["selector"]["info"]["credit_cde"] = key_word

event_log = EventlogOld.from_dict(log)
crawler = Crawler()
parser = Parser()
crawler.crawl(event_log)
parser.parse(event_log)
