# encoding=utf8

from urllib3 import disable_warnings
from resx.log import setup_logger
logger = setup_logger()

from eventlog_spider.common.eventlog import EventlogNew as Eventlog
from eventlog_spider.crawler.crawler_acftu import <PERSON><PERSON><PERSON>rawler as Crawler
from eventlog_spider.parser.parser_acftu import <PERSON><PERSON><PERSON><PERSON><PERSON> as Parser

disable_warnings()

# 复制此代码构建自己的本地测试代码，不用提交git

eventlog = Eventlog.from_dict(
{
    "event_id": "octopvs-credit-acftu-81210281661139536E-1738831230",
    "spider_code": -1,
    "selector": {
        "send_ts": 1738831230,
        "receive_ts": -1,
        "reason": "schedule",
        "clue": False,
        "entry_name": "credit",
        "inst_name": "acftu",
        "word": "81210281661139536E",
        "info": {
            "name": "瓦房店市复州城中心敬老院工会委员会",
            "establish_date": None,
            "cid": 2445408060,
            "status": "正常",
            "reg_number": None,
            "province": "辽宁省",
            "city": "大连市"
        },
        "try_id": 2,
        "meta": {},
        "weight": 952
    },
    "spider": {
        "receive_ts": -1,
        "send_ts": -1,
        "spider_data": {},
        "ab_info": {}
    }
})

crawler = Crawler()
parser = Parser()

crawler.crawl(eventlog)
parser.parse(eventlog)
logger.info(f'after parser {eventlog.model_dump_json()}')
