import time
from urllib3 import disable_warnings
from resx.log import setup_logger
from resx.mysql_client import MySQLClient
from resx.config import *
import os

logger = setup_logger(use_crawler_log=True, crawler_log_enable=False, name=__name__, debug=False)
from eventlog_spider.crawler.crawler_gds import GdsCrawler as Crawler
from eventlog_spider.parser.parser_gds import GdsParser as Parser
from eventlog_spider.common.eventlog import EventlogNew as Eventlog, SpiderCode
from biz_utils.entity.company import Company, CompanyDao

disable_warnings()
company_dao = CompanyDao()
os.environ['POD_ENV'] = 'online'


def get_eventlog(name):
    company: Company = company_dao.get(name=name)
    return Eventlog.from_dict(
        {"event_id": "octopvs-credit-gds-92222403MA1436GB71-1720162588",
         "code": -1,
         "selector": {
             "send_ts": 1720162588,
             "receive_ts": -1,
             "reason": "platform",
             "clue": False,
             "entry_name": "credit",
             "inst_name": "gds",
             "word": company.credit_code,
             "info": {
                 "name": company.name,
                 "cid": company.cid,
                 "reg_number": company.reg_number,
             },
             "try_id": 0,
             "meta": {
                 # "barcode": "06970791520015",
             },
             "weight": 990
         },
         "spider": {
             "receive_ts": -1,
             "send_ts": -1,
             "spider_data": {}
         }
         }
    )


crawler = Crawler()
parser = Parser()
# 雅戈尔时尚股份有限公司 雅戈尔时尚（上海）科技有限公司
for i in ('雅戈尔时尚（上海）科技有限公司',):
    a = get_eventlog(i)
    crawler.crawl(a)
    parser.parse(a)
    logger.info(f'after parser {a.model_dump_json()}')
