# encoding=utf8
from biz_utils.entity.company_graph import CompanyGraphDao, CompanyGraph
from biz_utils.gs_enum import EntityType
from biz_utils.entity.human_graph import HumanGraph, HumanGraphDao
from resx.log import setup_logger

logger = setup_logger(name=__name__)


class GraphIdService(object):
    def __init__(self):
        self.company_graph_dao = CompanyGraphDao()
        self.human_graph_dao = HumanGraphDao()

    def get_graph_id(self, entity_type: EntityType, raw_id: int) -> int:
        """
        :param entity_type: EntityType
        :param raw_id: cid or hid
        :return: graph_id or 0 if errors
        """
        if entity_type == EntityType.HUMAN or entity_type == EntityType.TYPE3:
            human_graph: HumanGraph = self.human_graph_dao.get(human_id=raw_id)
            if human_graph:
                return human_graph.graph_id
            return 0
        if entity_type == EntityType.ORG:
            company_graph: CompanyGraph = self.company_graph_dao.get(company_id=raw_id)
            if company_graph:
                return company_graph.graph_id
            return 0
        logger.warning(f'bad entity_type={entity_type}')
        return 0
