# encoding=utf8

import time
import ddddocr
from resx.redis_types import RedisQueue
from resx.config import *
from resx.req import Req<PERSON>anager, URLPat, Response
from eventlog_spider.common.eventlog import EventlogNew as Eventlog, SpiderCode
from eventlog_spider.crawler.crawler import Crawler, CrawlerTask
from resx.log import setup_logger

logger = setup_logger(name=__name__)


class ACFTUCrawlerTask(CrawlerTask):
    def __init__(self, eventlog: Eventlog):
        super().__init__(eventlog)


class ACFTUCrawler(Crawler):
    def __init__(self, **kwargs):
        self.req_manager = ReqManager(
            pats=[
                URLPat('captcha', 'https://work.acftu.org/legal_management/check/getKaptchaCode?d={d}', validate_func=self.response_captcha_validate),
                URLPat('detail', 'https://work.acftu.org/legal_management/jdcx/selectTJgdm?tyshxydm={word}&code={captcha}', timeout=30, validate_func=self.response_detail_validate),
            ],
            default_headers={
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'zh,en-US;q=0.9,en;q=0.8,ru;q=0.7,zh-CN;q=0.6,zh-TW;q=0.5',
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Pragma': 'no-cache',
                'Referer': 'https://work.acftu.org/legal_management/',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-origin',
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36',
                'sec-ch-ua': '"Google Chrome";v="123", "Not:A-Brand";v="8", "Chromium";v="123"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"macOS"',
            }
        )
        super().__init__(input_queue=RedisQueue(name='acftu', **CFG_REDIS_GS, db=9), task_cls=ACFTUCrawlerTask, eventlog_class=Eventlog, **kwargs)

    @classmethod
    def get_name(cls):
        return 'acftu'

    def do_crawl(self):
        task: ACFTUCrawlerTask = self.get_crawler_task()
        eventlog: Eventlog = task.eventlog
        task.pages = dict()

        ctx = {}
        resp_captcha = self.req_manager.request(pat='captcha', d=int(time.time() * 1000), response_validate_ctx=ctx)
        if not resp_captcha:
            logger.warning(f'error captcha {eventlog.event_id}')
            eventlog.spider_code = SpiderCode.FAIL
            return
        task.pages['captcha.jpg'] = resp_captcha.content
        captcha_code = ctx['captcha_code']

        resp_detail = self.req_manager.request(pat='detail', word=eventlog.selector.word, captcha=captcha_code)
        if not resp_detail or resp_detail.status_code != 200:
            logger.warning(f'error detail {eventlog.event_id} {resp_detail}')
            eventlog.spider_code = SpiderCode.FAIL
            return
        task.pages['detail.txt'] = resp_detail.text

    @staticmethod
    def response_detail_validate(response: Response, ctx) -> bool:
        if response.status_code != 200:
            return False
        logger.info(f'detail === {response.text}')
        if '今日剩余查询总数为0' in response.text:
            return False
        if '验证码输入错误' in response.text:
            return False
        return True

    @staticmethod
    def response_captcha_validate(response: Response, ctx) -> bool:
        ocr = ddddocr.DdddOcr(show_ad=False)
        captcha_code = ocr.classification(response.content)
        ctx['captcha_code'] = captcha_code
        if not captcha_code or len(captcha_code) != 4:
            return False
        return True
