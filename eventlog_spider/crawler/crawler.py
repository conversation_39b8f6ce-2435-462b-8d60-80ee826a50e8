# encoding=utf8
from abc import abstractmethod
from typing import Dict, Optional
from threading import Lock, current_thread
import traceback
import requests
import resx.func
from requests.adapters import HTTPAdapter
from requests import Response, Session
import time
from typing import Union, List, Dict, Any
import re
import random
from resx.func import cur_ts_sec
from resx.redis_types import RedisQueue
from eventlog_spider.common.eventlog import Eventlog, EventlogOld, EventlogNew
from eventlog_spider.common.obs_manager import OBSManager
from resx.log import setup_logger
from crawler_log.log_init_v2 import Log
import base64
from collections import OrderedDict

logger = setup_logger(name=__name__)


# Crawler的每个线程holder一个CrawlerTask，对应一个eventlog
class CrawlerTask(object):
    def __init__(self, eventlog: Eventlog):
        self.eventlog: Eventlog = eventlog
        self.event_id = self.eventlog.event_id
        self.pages: Dict[str, str] = dict()
        self.crawl_cache: Optional[Dict[str, str]] = None

        self.session = requests.session()
        self.session.proxies = {
            'http': 'http://************:30636',
            'https': 'http://************:30636'
        }
        self.session.mount('http://', HTTPAdapter(max_retries=2))
        self.session.mount('https://', HTTPAdapter(max_retries=2))

        info = eventlog.selector.info
        if hasattr(eventlog.selector, 'word') or 'keyword' in info:
            self.keyword = eventlog.selector.get_info('keyword') or eventlog.selector.word
        else:
            for k, v in info.items():
                if re.search('[A-Z0-9]{18}', v):
                    self.keyword = v
                    break
            else:
                k, v = next(iter(info.items()))
                self.keyword = v

        if hasattr(eventlog, 'spider_code') and hasattr(eventlog, 'crawler'):
            eventlog.crawler["keyWord"] = self.keyword
            eventlog.crawler["processId"] = f"{resx.func.get_my_ip()}-{current_thread().ident}"
        if isinstance(logger, Log):
            logger.ParamManger().add_params(keyword=self.keyword, company=self.keyword)


class Crawler(object):
    def __init__(self, input_queue: RedisQueue, eventlog_class=EventlogOld, task_cls=CrawlerTask, page_bak_count: int = 3):
        self.eventlog_class = eventlog_class
        self.task_cls = task_cls
        self.page_bak_count = page_bak_count
        self.obs_manager = OBSManager()
        self.tasks: Dict[int, task_cls] = dict()
        self.tasks_lock = Lock()
        self.input_queue = input_queue

    def read_cache(self, eventlog: Eventlog):
        with self.tasks_lock:
            tid = current_thread().ident
        if self.tasks[tid].crawl_cache is None:
            cache_obs_path = f'page/{self.get_name()}/{self.tasks[tid].keyword}/cache.json'
            self.tasks[tid].crawl_cache = self.obs_manager.download_cache(cache_obs_path)

    @classmethod
    @abstractmethod
    def get_name(cls):
        pass

    def crawl(self, eventlog: Eventlog) -> Eventlog:
        logger.info(f'==== BEGIN {eventlog}')

        with self.tasks_lock:
            tid = current_thread().ident
            self.tasks[tid] = self.task_cls(eventlog)

        if hasattr(eventlog, 'spider'):
            eventlog.spider.receive_ts = cur_ts_sec()
        self.do_crawl()
        task = self.get_crawler_task()

        # send responses to OBS
        page_ts = self.obs_manager.upload_pages(
            pages=task.pages,
            bak_count=self.page_bak_count,
            base_dir=f'page/{self.get_name()}/{task.keyword}',
        )

        if task.crawl_cache is not None:
            self.obs_manager.upload_cache(
                cache=task.crawl_cache,
                bak_count=1,
                cache_base_dir=f'page/{self.get_name()}/{task.keyword}/cache.json',
            )

        if hasattr(eventlog, 'spider') and page_ts > 0:
            eventlog.spider.spider_data['page_ts'] = page_ts
        if hasattr(eventlog, 'parser') and page_ts > 0:
            eventlog.parser.data['page_ts'] = page_ts

        return eventlog

    @abstractmethod
    def do_crawl(self):
        pass

    def get_crawler_task(self):
        with self.tasks_lock:
            tid = current_thread().ident
            if tid not in self.tasks:
                raise RuntimeError(f'not task tid={tid}')
            return self.tasks[tid]


class CrawlerTools:
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 "
                      "Safari/537.36",
    }

    @staticmethod
    def request(session_or_task: Union[Session, CrawlerTask], method: str, url: str, params: dict = None, data: Union[dict, str] = None, json: dict = None,
                timeout=3, headers=None, path: str = None, name: str = '', tojson=False, toRaw=False, long_proxy=False, long_timeout=30, isDetail=False,
                **kwargs) -> Union[dict, str, Response]:
        session = session_or_task if isinstance(session_or_task, Session) else session_or_task.session
        for i in range(5):
            response = None
            try:
                a = time.time()
                request_params = {'method': method, 'url': url, 'data': data, 'headers': headers if headers else CrawlerTools.headers,
                                  'verify': False, 'timeout': timeout, 'params': params, 'json': json, **kwargs}
                if long_proxy:
                    request_params['proxies'] = CrawlerTools.get_long_proxy()
                    request_params['timeout'] = long_timeout
                response = session.request(**request_params)

                if response.status_code != 200 or 'Please try again!' in response.text:
                    logger.warning(f'{name}-{i + 1} --> {response.status_code}')
                    del session.cookies['proxyBase']
                    continue
                logger.info(f'{name} --> {response.status_code} --> time: {time.time() - a}')

                if toRaw:
                    return response
                if isDetail:
                    a = re.sub(r'[\n\r\t]', '', response.text)
                    logger.info(f'{name} --> {a}')
                if tojson:
                    return response.json()
                return response.text
            except MyException as e:
                raise e
            except (requests.exceptions.ConnectionError, requests.exceptions.Timeout) as e:
                logger.warning(f'{name} --> continue{i} exception: {e}')
                del session.cookies['proxyBase']
            except Exception as e:
                status = response.status_code if response else "空"
                text = response.text if response else "空"
                logger.warning(f'{name} --> continue{i} 状态码：{status} res: {text} exception: {e}')
                del session.cookies['proxyBase']
        raise MyException('接口连续失败')

    @staticmethod
    def get_long_proxy():
        res = requests.get('http://10.99.192.206:8015/long-proxy')
        proxy = random.choice(res.text.split('\r\n'))
        return {
            'http': f'http://{proxy}',
            'https': f'http://{proxy}'
        }

    @staticmethod
    def custom_traceback(e):
        exc_type = type(e)
        exc_value = e
        tb = e.__traceback__
        """
        自定义打印 traceback，仅显示用户代码的报错信息
        """
        # 提取 traceback 信息
        tb_list = traceback.extract_tb(tb)
        user_tb = [frame for frame in tb_list if 'eventlog-spider' in frame.filename]
        error = '\n'
        if user_tb:
            # print("\033[91mTraceback (most recent call last):\033[0m")
            # 格式化并打印用户代码的 traceback 信息
            # print("\033[91m" + "".join(traceback.format_list(user_tb)) + "\033[0m")
            # print(f"\033[91m{exc_type.__name__}: {exc_value}\033[0m")
            error += "".join(traceback.format_list(user_tb)) + f"{exc_type.__name__}: {exc_value}"
        else:
            # print(f"\033[91m{exc_type.__name__}: {exc_value}\033[0m")
            error += f"{exc_type.__name__}: {exc_value}"
        return error

    @staticmethod
    def remove_None(dict_object):
        delete_key = []
        for k, v in dict_object.items():
            if not v:
                delete_key.append(k)
        for k in delete_key:
            del dict_object[k]

    @staticmethod
    def get_new_session() -> Session:
        session = requests.session()
        session.proxies = {'http': 'http://************:30636', 'https': 'http://************:30636'}
        session.mount('http://', HTTPAdapter(max_retries=2))
        session.mount('https://', HTTPAdapter(max_retries=2))
        return session

    @staticmethod
    def contains_chinese(text):
        pattern = re.compile(r'[\u4e00-\u9fa5]+')
        return bool(pattern.search(text))

    @staticmethod
    def get_ip(session: Session):
        try:
            ip = base64.b64decode(session.cookies.get_dict().get("proxyBase", "")).decode()
            return ip
        except Exception as e:
            logger.error(f'get_ip error: {CrawlerTools.custom_traceback(e)}')
            return ""

    @staticmethod
    def unique(my_list, exclude_field: str = ''):
        unique_list = []
        # history = {}
        for d in my_list:
            filtered_items = tuple((k, v) for k, v in sorted(d.items()) if k != exclude_field)
            unique_list.append(filtered_items)
            # history.update({d["uniscid"]: d[exclude_field]})
        unique_list = list(OrderedDict.fromkeys(unique_list))
        data = []
        for t in unique_list:
            d = dict(t)
            # d.update({exclude_field: history[d['uniscid']]})
            data.append(d)
        return data

    @staticmethod
    def deduplicate_by_field(
            arr: List[Dict[str, Any]],
            field: str,
            keep: str = 'first',  # 'first' or 'last'
            ignore_missing: bool = False  # True：字段缺失则跳过；False：字段缺失时抛 ValueError
    ) -> List[Dict[str, Any]]:
        """
        根据指定字段对字典列表去重。

        :param arr:      待去重的列表，元素为 dict。
        :param field:    用来判断唯一性的 key 名称。
        :param keep:     'first'  保留首次出现的元素（稳定排序）
                         'last'   保留最后一次出现的元素
        :param ignore_missing:  当字典中缺少该字段时：True=跳过该元素；False=抛异常
        :return:         去重后的新列表
        """

        seen = set()  # 用于记录已出现过的字段值
        result = []  # 最终结果

        if keep == 'first':
            for item in arr:
                if field not in item:
                    if ignore_missing:
                        continue
                    raise ValueError(f"Missing field '{field}' in element: {item}")

                key_val = item[field]
                if key_val not in seen:
                    seen.add(key_val)
                    result.append(item)

        else:  # keep == 'last'
            # 先逆序遍历，保留最后一次出现的元素，然后再翻转回来保持原顺序
            for item in reversed(arr):
                if field not in item:
                    if ignore_missing:
                        continue
                    raise ValueError(f"Missing field '{field}' in element: {item}")

                key_val = item[field]
                if key_val not in seen:
                    seen.add(key_val)
                    result.append(item)
            result.reverse()

        return result


class MyException(Exception):

    def __init__(self, message):
        self.message = message
        super().__init__(self.message)
