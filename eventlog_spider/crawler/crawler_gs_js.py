# encoding=utf8
import re
import math
import json
import time
import execjs
import requests
import random
import hashlib
from urllib.parse import quote
from concurrent.futures import ThreadPoolExecutor, as_completed

from resx.redis_types import RedisQueue
from resx.config import *
from eventlog_spider.common.eventlog import EventlogOld as Eventlog, SpiderCode
from eventlog_spider.crawler.crawler import Crawler, CrawlerTask
from resx.log import setup_logger

logger = setup_logger(name=__name__)


class GSJSCrawlerTask(CrawlerTask):
    def __init__(self, eventlog: Eventlog):
        self.search = eventlog.selector.get_info('keyword')
        self.name = eventlog.selector.get_info('name')
        self.credit_code = eventlog.selector.get_info('credit_code')
        self.session_ins = requests.session()
        super().__init__(eventlog)


class GSJSCrawler(Crawler):
    def __init__(self, **kwargs):
        self.LONG_PROXY_URL = "http://*************:8015/long-proxy"
        self.GONG_SHANG_HOMEPAGE = "http://www.jsgsj.gov.cn:58888/ecipplatform/login.html"
        self.USERAGENT = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        self.ENC_SERVER = "http://jsgsj-server-python.jindidata.com"
        self.js_executor = self.init_js_executor()

        super().__init__(
            input_queue=RedisQueue(name='searchCompany_jsold_queue_updateOffline', **CFG_REDIS_GS, db=5),
            task_cls=GSJSCrawlerTask,
            eventlog_class=Eventlog,
            **kwargs)

    @classmethod
    def get_name(cls):
        return 'js'

    @staticmethod
    def init_js_executor():
        with open(f"../eventlog_spider/scripts/jiangsu_etp.js") as t:
            result = execjs.compile(t.read())
        return result

    def do_crawl(self):
        task = self.get_crawler_task()
        eventlog = task.eventlog

        if task.search is None:
            logger.warning(f'no keyword {eventlog}')
            eventlog.spider_code = SpiderCode.GIVE_UP
            task.pages = {}
            return

        search_keyword = task.search
        task_pages = task.pages

        code_ = self.crawl_process(search_keyword, task_pages)

        if code_ == 500:
            eventlog.spider_code = SpiderCode.FAIL
            task.pages = {}
        elif code_ == 404:
            eventlog.spider_code = SpiderCode.SEARCH_EMPTY
            task.pages = {}
        elif code_ == 200:
            ...
        else:
            eventlog.spider_code = SpiderCode.GIVE_UP
            task.pages = {}

        logger.info(f"{task.pages}, code: {code_}")

    def crawl_process(self, search_keyword: str, task_pages: dict) -> int:
        """
        请求链路函数，处理爬虫的具体请求逻辑。

        参数:
        search_keyword (str): 要搜索的关键词。
        task_pages (dict): 存储抓取结果的字典。

        返回:
        int: 返回状态码，200表示成功，404表示未找到，500表示失败。
        """
        try:
            # 获取长代理 / 短代理
            proxy = self.long_proxy()
            proxies = {'http': 'http://' + proxy, 'https': 'http://' + proxy}
            logger.info(f"「{search_keyword}」: 抓取任务开始")

            # 认证IP
            client_ip = self.authentication_ip(proxies)
            logger.info(f"「{search_keyword}」: COOKIE初始化,IP认证 完成")

            # 请求工商首页 初始化COOKIE
            self.get_homepage(proxies)

            # 获取极验请求头
            gt, challenge = self.get_gt_challenge(proxies)
            time.sleep(0.5)

            # 认证搜索词
            self.authentication_search_word(proxies, search_keyword)
            logger.info(f"「{search_keyword}」: 极验初始化,搜索词认证 完成")

            # 获取 validate
            validate = self.get_validate(gt, challenge, proxy)
            logger.info(f"「{search_keyword}」: 极验计算 完成")

            # 获取 search_url
            url_json = self.get_search_url(search_keyword, challenge, validate, client_ip)
            search_id = self.get_search_id(proxies, url_json["url"], url_json["origin_data"],
                                           url_json["decrypt_data"], url_json["timestamp"], search_keyword,
                                           challenge, validate)

            # 构造请求连接
            req_url_json = self.prd_req_url(search_id, 1)

            # 发起请求进行注册
            content = self.get_search_page(proxies, req_url_json["etp"], req_url_json["list_url"])
            logger.info(f"「{search_keyword}」: 加密,请求链接构造,注册 完成")

            # 请求详情页
            time.sleep(1)
            task_pages["search_list.txt"] = content
            detail_response, etp, enc_method, enc_data = self.get_detail_page(proxies, content, search_id, client_ip)
            if not detail_response.json():  # 尝试解析
                logger.info("抓取为空")
                return 404
            task_pages["detail_info.txt"] = detail_response.text
            logger.info(f"「{search_keyword}」: 详情页获取 完成 - {detail_response.text}")

            # 请求股东
            time.sleep(0.4)
            query_type = "Gdcz"
            response = self.get_general_info(proxies, etp, enc_method, enc_data, query_type=query_type, page_no=1)
            task_pages["investor_list_1.txt"] = response.text
            max_page = math.ceil(response.json().get("totalRows", "") / 5) if response.json().get("totalRows", 0) else 0
            logger.info(f"「{search_keyword}」: 股东获取 完成 - {response.text}")
            result_list = self.get_contribution_detail(response.json()["data"], proxies)
            task_pages["investor_info_list_1.txt"] = json.dumps(result_list, ensure_ascii=False)
            logger.info(f"「{search_keyword}」: 股东出资获取 完成 - {result_list}")

            if max_page > 1:
                for _ in range(max_page - 1):
                    time.sleep(0.2)
                    page_no = _ + 2
                    response = self.get_general_info(proxies, etp, enc_method, enc_data, query_type=query_type,
                                                     page_no=page_no)
                    task_pages[f"investor_list_{page_no}.txt"] = response.text
                    logger.info(f"「{search_keyword}」: 股东获取 完成 - {response.text}")
                    result_list = self.get_contribution_detail(response.json()["data"], proxies)
                    task_pages[f"investor_info_list_{page_no}.txt"] = json.dumps(result_list, ensure_ascii=False)
                    logger.info(f"「{search_keyword}」: 股东出资获取 完成 - {result_list}")

            # 请求主要人员
            time.sleep(0.4)
            query_type = "Zyry"
            response = self.get_general_info(proxies, etp, enc_method, enc_data, query_type=query_type, page_no=1)
            task_pages[f"staff_list.txt"] = response.text
            response.json()
            logger.info(f"「{search_keyword}」: 主要人员获取 完成 - {response.text}")

            # 请求变更记录
            time.sleep(0.4)
            query_type = "Bgxx"
            response = self.get_general_info(proxies, etp, enc_method, enc_data, query_type=query_type, page_no=1)
            task_pages[f"change_list_1.txt"] = response.text
            logger.info(f"「{search_keyword}」: 变更记录获取 完成 - {response.text}")
            bgxx_max_page = math.ceil(response.json().get("totalRows", "") / 5) if response.json().get("totalRows",
                                                                                                       0) else 0
            if bgxx_max_page > 1:
                for _ in range(bgxx_max_page - 1):
                    time.sleep(0.2)
                    page_no = _ + 2
                    response = self.get_general_info(proxies, etp, enc_method, enc_data, query_type=query_type,
                                                     page_no=page_no)
                    task_pages[f"change_list_{page_no}.txt"] = response.text
                    logger.info(f"「{search_keyword}」: 变更记录获取 完成 - {response.text}")

            # 经营异常
            time.sleep(0.4)
            query_type = "Jyyc"
            response = self.get_general_info(proxies, etp, enc_method, enc_data, query_type=query_type, page_no=1)
            task_pages[f"abnormal_list_1.txt"] = response.text
            logger.info(f"「{search_keyword}」: 经营异常获取 完成 - {response.text}")
            jyyc_max_page = math.ceil(response.json().get("totalRows", "") / 5) if response.json().get("totalRows", 0) else 0
            if jyyc_max_page > 1:
                for _ in range(jyyc_max_page - 1):
                    time.sleep(0.2)
                    page_no = _ + 2
                    response = self.get_general_info(proxies, etp, enc_method, enc_data, query_type=query_type,
                                                     page_no=page_no)
                    task_pages[f"abnormal_list_{page_no}.txt"] = response.text
                    logger.info(f"「{search_keyword}」: 经营异常获取 完成 - {response.text}")

            logger.info(f"「{search_keyword}」: 抓取结束")
            return 200

        except Exception as e:
            logger.warning(e)
            return 500

    def get_contribution_detail(self, investor_list: list, proxies: dict) -> list:
        """
        获取投资者的出资详情。

        参数:
        investor_list (list): 投资者列表。
        session (requests.Session): 当前会话。
        proxies (dict): 代理设置。

        返回:
        list: 投资者出资详情列表。
        """
        result_list = []
        for investor in investor_list:
            result = {}
            if investor["SHOW"] in ("0", "1"):
                result["investor_id"] = investor["ID"]
                # 初始化加密信息
                details_result = json.loads(
                    self.js_executor.call("get_investor_info", investor["ORG"], investor["ID"], investor["SEQ_ID"], "",
                                          investor["CAPI_TYPE_NAME"]))

                etp = details_result["ori"]["etp"]

                ori_url = details_result["ori"]["detail_url"]
                rj_url = details_result["rj"]["detail_url"]
                sj_url = details_result["sj"]["detail_url"]

                headers = {
                    "Accept": "application/json, text/javascript, */*; q=0.01",
                    "Accept-Language": "zh-CN,zh;q=0.9",
                    "Cache-Control": "no-cache",
                    "Content-Length": "0",
                    "Origin": "http://www.jsgsj.gov.cn:58888",
                    "Pragma": "no-cache",
                    "Proxy-Connection": "keep-alive",
                    "Referer": "http://www.jsgsj.gov.cn:58888/ecipplatform/page/jiangsuDetail/detailspage/gdcz/gdczDetail.html?org=876&id=529209&seqId=44&capiTypeName=ED841C1B8D179CE24593F28D164C44BF&admitMain=08&IFFQR=1&ecid=9A75974EF3D51CD3AE77B5361BB2DE84&eitp=hlDEk+xyErgzToWr0MZxPmbbIqDYuD/r2cm9sZV4h83sHDSLrvSvyQo8qVS9pz6v0GsB9xyjRI0RqzfpTDHY8Q==&etp=8BNKkhKGybGXzQ+6L5aKsw==&esn=dQ5d7QL3Y8q0NL6VdZKswEC82TXneqqw0eBaU62PugI=",
                    "User-Agent": self.USERAGENT,
                    "X-Requested-With": "XMLHttpRequest",
                    "etp": etp
                }

                # 出资信息
                response = self.session_post_retry(type_="POST", url=ori_url, headers=headers, proxies=proxies,
                                                   timeout=5)
                result["ori"] = response.json()

                # 认缴
                response = self.session_post_retry(type_="POST", url=rj_url, headers=headers, proxies=proxies,
                                                   timeout=5)
                result["rj"] = response.json()

                # 实缴
                response = self.session_post_retry(type_="POST", url=sj_url, headers=headers, proxies=proxies,
                                                   timeout=5)
                result["sj"] = response.json()

                result_list.append(result)
        return result_list

    def get_general_info(self, proxies: dict, etp: str,
                         enc_method: str, enc_data: str, query_type: str, page_no: int) -> requests.Response:
        """
        通用获取信息函数。

        参数:
        session (requests.Session): 当前会话。
        proxies (dict): 代理设置。
        etp (str): 加密信息。
        enc_method (str): 加密方法。
        enc_data (str): 加密数据。
        query_type (str): 查询类型。
        page_no (int): 当前页码。

        返回:
        requests.Response: 响应对象。
        """
        headers = {
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "Accept-Language": "zh-CN,zh;q=0.9",
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Length": "0",
            "Origin": "http://www.jsgsj.gov.cn:58888",
            "Pragma": "no-cache",
            "User-Agent": self.USERAGENT,
            "X-Requested-With": "XMLHttpRequest",
            "etp": etp,
        }
        url = f"http://www.jsgsj.gov.cn:58888/ecipplatform/{enc_method}.json?query{query_type}=true&typeMethod=md5Method&{enc_data}"
        data = {
            "pageSize": "5",
            "curPage": f"{page_no}",
            "sortName": "",
            "sortOrder": ""
        }
        response = self.session_post_retry(type_="POST", url=url, headers=headers, proxies=proxies, data=data,
                                           timeout=10)
        return response

    def get_detail_page(self, proxies: dict, content: str,
                        search_id: str, client_ip: str) -> tuple:
        """
        获取详情页信息。

        参数:
        session (requests.Session): 当前会话。
        proxies (dict): 代理设置。
        content (str): 内容。
        search_id (str): 搜索ID。
        client_ip (str): 客户端IP。

        返回:
        tuple: 包含响应对象、加密信息、加密方法和加密数据的元组。
        """
        company_info = json.loads(content)['items'][0]
        details_result = json.loads(
            self.js_executor.call("get_details", company_info["ORG"], company_info["ID"], company_info["SEQ_ID"], "",
                                  search_id, client_ip))

        etp = details_result["etp"]
        detail_url = details_result["detail_url"]
        encry_method = details_result["encryMethod"]
        encry_data = details_result["encryData"]

        headers = {
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "Accept-Language": "zh-CN,zh;q=0.9",
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Length": "0",
            "Origin": "http://www.jsgsj.gov.cn:58888",
            "Pragma": "no-cache",
            "User-Agent": self.USERAGENT,
            "X-Requested-With": "XMLHttpRequest",
            "etp": etp,
        }
        response = self.session_post_retry(type_="POST", url=detail_url, headers=headers, proxies=proxies, timeout=10)
        return response, etp, encry_method, encry_data

    def get_search_page(self, proxies: dict, etp: str,
                        search_url: str) -> str:
        headers = {
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "Accept-Language": "zh-CN,zh;q=0.9",
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Length": "0",
            "Origin": "http://www.jsgsj.gov.cn:58888",
            "Pragma": "no-cache",
            "Referer": "http://www.jsgsj.gov.cn:58888/ecipplatform/webnew/jiangsulist/jiangsulist.jsp",
            "User-Agent": self.USERAGENT,
            "X-Requested-With": "XMLHttpRequest",
            "etp": etp,
        }
        response = self.session_post_retry(type_="POST", url=search_url, headers=headers, proxies=proxies, timeout=5)
        return response.text

    def get_search_id(self, proxies: dict, url: str,
                      origin: str, decrypt: str, timestamp: str, search_key: str,
                      challenge: str, validate: str) -> str:
        headers = {
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "Accept-Language": "zh-CN,zh;q=0.9",
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
            "Origin-Data": origin,
            "Decrypt-Data": decrypt,
            "timestamp": timestamp,
            "Origin": "http://www.jsgsj.gov.cn:58888",
            "Pragma": "no-cache",
            "Referer": "http://www.jsgsj.gov.cn:58888/ecipplatform/login.html",
            "User-Agent": self.USERAGENT,
            "X-Requested-With": "XMLHttpRequest",
        }
        data = {
            "type": "search",
            "name": search_key,
            "geetest_challenge": challenge,
            "geetest_validate": validate,
            "geetest_seccode": validate + "|jordan",
        }
        response = self.session_post_retry(type_="POST", url=url, data=data, headers=headers, proxies=proxies, timeout=5)
        logger.info(response.text)
        search_id = response.json()['name']
        return search_id

    def get_search_url(self, search_key: str, challenge: str, validate: str,
                       client_ip: str) -> dict:
        etp = self.js_executor.call("get_etp")  # 调get_etp

        # 时间认证
        while True:
            ran = str(random.random())[2:8]
            t = str(int(time.time() * 1000))
            origin = f"{t}@@{quote(search_key)}@@{str(ran)}@@{challenge}@@{validate}@@{validate}|jordan@@{client_ip}"
            decrypt = self.hashlib_func(origin.encode()).hexdigest()
            if decrypt.startswith("0222"):
                break
        result = {
            'origin_data': origin,
            'decrypt_data': decrypt,
            'ran': ran,
            'timestamp': str(t),
            'url': f"http://www.jsgsj.gov.cn:58888/ecipplatform/geetestController0222.json?validate=true{etp}"
        }
        return result

    @staticmethod
    def hashlib_func(data: bytes) -> hashlib.md5:
        return hashlib.md5(data)

    def get_validate(self, gt: str, challenge: str, proxy: str) -> str:
        api = "http://*************:8881/getGeetest3Validate?gt=" + gt + "&challenge=" + challenge + "&ua=" + quote(
            self.USERAGENT) + "&proxy=" + proxy + "&ref=" + quote('http://www.jsgsj.gov.cn:58888/')

        # 使用并发请求
        def fetch_validate(url_api):
            try:
                response = requests.get(url_api, timeout=15)
            except TimeoutError:
                logger.warning("请求超时，返回空结果")  # 记录超时日志
                return {}
            except Exception as e:
                logger.warning(f"请求发生异常: {e}")  # 记录其他异常
                return {}
            return response.json()

        with ThreadPoolExecutor(max_workers=3) as executor:
            futures = [executor.submit(fetch_validate, api) for _ in range(3)]
            for future in as_completed(futures):
                result = future.result()
                if 'validate' in result:
                    return result['validate']  # 返回找到的validate

        return ""

    def prd_req_url(self, search_key: str, page_no: int) -> dict:
        result = json.loads(self.js_executor.call("get_end", search_key, page_no))
        return result

    def authentication_search_word(self, proxies: dict,
                                   search_word: str) -> None:
        headers = {
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "Accept-Language": "zh-CN,zh;q=0.9",
            "Cache-Control": "no-cache",
            "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
            "Origin": "http://www.jsgsj.gov.cn:58888",
            "Pragma": "no-cache",
            "Referer": "http://www.jsgsj.gov.cn:58888/ecipplatform/login.html",
            "User-Agent": self.USERAGENT,
            "X-Requested-With": "XMLHttpRequest"
        }

        url = 'http://www.jsgsj.gov.cn:58888/ecipplatform/infoQueryController.json?queryNowday1211=true'
        self.session_post_retry(type_="POST", url=url, headers=headers, proxies=proxies, timeout=5)

        url = 'http://www.jsgsj.gov.cn:58888/ecipplatform/infoQueryController.json?getDisableWord=true'
        data = {
            "name": search_word
        }
        self.session_post_retry(type_="POST", url=url, headers=headers, data=data, proxies=proxies, timeout=5)

    def get_gt_challenge(self, proxies: dict) -> tuple:
        req_time = str(int(time.time() * 1000))
        headers = {
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "Accept-Language": "zh-CN,zh;q=0.9",
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Length": "0",
            "Origin": "http://www.jsgsj.gov.cn:58888",
            "Pragma": "no-cache",
            "Referer": "http://www.jsgsj.gov.cn:58888/ecipplatform/login.html",
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "X-Requested-With": "XMLHttpRequest",
            "timestamp": req_time
        }
        url = "http://www.jsgsj.gov.cn:58888/ecipplatform/geetestController0222.json"
        params = {
            "queryGetestInit": "true",
            "pageFromPlatform": "web",
            "t": req_time
        }
        response = self.session_post_retry(type_="POST", url=url, headers=headers, params=params, proxies=proxies)
        return response.json()["gt"], response.json()["challenge"]

    def sha1_python(self, message):
        sha1_hash = hashlib.sha1(message.encode('utf-8')).digest()
        binary_str = ''.join(format(byte, '08b') for byte in sha1_hash)
        return binary_str

    def get_requests(self, cookies=None, proxies=None):
        task = self.get_crawler_task()

        headers = {
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Accept-Language": "zh-CN,zh;q=0.9",
            "Cache-Control": "no-cache",
            "Pragma": "no-cache",
            "Proxy-Connection": "keep-alive",
            "Referer": "http://www.jsgsj.gov.cn:58888/ecipplatform/infoQueryController.json?queryClientIp=true",
            "Upgrade-Insecure-Requests": "1",
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        }
        url = "http://www.jsgsj.gov.cn:58888/ecipplatform/infoQueryController.json"
        params = {
            "queryClientIp": "true"
        }
        response = task.session_ins.get(url, headers=headers, params=params, cookies=cookies, verify=False, proxies=proxies)

        if not cookies:
            prefix = re.findall("""var prefix = '(.*)';""", response.text)
            return prefix[0], response.cookies.get_dict()

        return response

    def verify_ip(self, proxies):
        prefix, cookies = self.get_requests(cookies=None, proxies=proxies)
        print(prefix, cookies)
        cnt = 0
        hex_str = ""
        while True:
            hash = self.sha1_python(prefix + hex_str)
            if hash[0:9] == "000000000":
                break
            cnt += 1
            hex_str = format(cnt, 'x')
        cookies["safeline_verify"] = cookies["safeline_verify"] + f"{hex_str};Max-Age=60"
        print(cookies)
        response = self.get_requests(cookies, proxies)
        return response

    def authentication_ip(self, proxies: dict) -> str:
        """ 请求工商接口 认证IP """
        response = self.verify_ip(proxies)
        client_ip = response.json()['clientIp']
        return client_ip

    def get_homepage(self, proxies: dict) -> None:
        """ 请求工商首页 初始化COOKIE """
        headers = {
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9",
            "User-Agent": self.USERAGENT
        }
        self.session_post_retry(type_="GET", url=self.GONG_SHANG_HOMEPAGE, headers=headers, proxies=proxies, timeout=5)

    def long_proxy(self) -> str:
        """ 获取 长代理 减小代理失效的几率 """
        headers = {
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "zh-CN,zh;q=0.9",
            "User-Agent": self.USERAGENT
        }
        response = requests.get(self.LONG_PROXY_URL, headers)
        proxy_list = response.text.split('\n')
        return random.choice(proxy_list).strip("\r")

    def session_post_retry(self, type_, url, headers, proxies, data=None, timeout=3, params=None):
        task = self.get_crawler_task()

        for _ in range(3):
            try:
                if type_ == "GET":
                    response = task.session_ins.get(url=url, headers=headers, proxies=proxies, data=data, params=params,
                                                    timeout=timeout, verify=False)
                elif type_ == "POST":
                    response = task.session_ins.post(url=url, headers=headers, proxies=proxies, data=data,
                                                     params=params, timeout=timeout, verify=False)
                else:
                    raise Exception("Session Params Error")

                if response.status_code != 200:
                    time.sleep(0.5)
                    logger.warning(f"「{task.search}」: 请求响应吗{response.status_code},请求重试。url={url}")
                    # logger.warning(response.text)
                    continue
                return response
            except Exception as e:
                logger.warning(e)
                if "HTTPConnectionPool" in str(e):
                    raise Exception("Proxy Outline")
                continue
        raise Exception("Unknown")
