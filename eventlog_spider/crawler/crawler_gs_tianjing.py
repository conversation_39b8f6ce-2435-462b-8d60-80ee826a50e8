from bs4 import BeautifulSoup
import re
import json

from resx.redis_types import RedisQueue, Redis
from resx.config import *
from eventlog_spider.common.eventlog import EventlogOld as Eventlog, SpiderCode
from eventlog_spider.crawler.crawler import Crawler, CrawlerTask, MyException, CrawlerTools
from crawler_log.log_init_v2 import getLogger
from eventlog_spider.parser.parser import ParseTools

logger = getLogger(__name__)


class TJCrawlerTask(CrawlerTask):
    pass


class TJCrawler(Crawler, CrawlerTools):

    @classmethod
    def get_name(cls):
        return 'tj'

    def __init__(self, **kwargs):
        self.headers = {
            "Origin": "http://credit.scjg.tj.gov.cn",
            "Referer": "http://credit.scjg.tj.gov.cn/gsxt/",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHT<PERSON>, like Gecko) Chrome/123.0.0.0 Safari/537.36 Edg/123.0.0.0",
        }
        self.timeout = 10
        self.search_url = 'https://credit.scjg.tj.gov.cn/es/ent/searchByGsxt.do'
        super().__init__(input_queue=RedisQueue(name='octopus_output_company_tj', **CFG_REDIS_GS, db=5), task_cls=TJCrawlerTask, eventlog_class=Eventlog,
                         **kwargs)

    def do_crawl(self):
        task: TJCrawlerTask = self.get_crawler_task()
        eventlog = task.eventlog

        if task.keyword is None:
            logger.warning(f'no keyword {eventlog}')
            eventlog.spider_code = SpiderCode.GIVE_UP
            task.pages = {}
            return

        try:
            task.pages = self.crawl_(task)
        except MyException as e:
            if e.message == '搜索为空':
                logger.warning(f'{task.keyword} - 搜索为空')
                eventlog.spider_code = SpiderCode.SEARCH_EMPTY
            if e.message == '接口连续失败':
                logger.warning(f'{task.keyword} - 接口连续失败')
                eventlog.spider_code = SpiderCode.FAIL
            task.pages = {}
        except Exception as e:
            raise e

    def crawl_(self, task: TJCrawlerTask):
        pages = dict()
        company_info = self.search(task)
        pages['company_info.txt'] = json.dumps(company_info, ensure_ascii=False)
        entid = company_info['entid']

        investor_info = self.investor(task, entid)
        pages['investor_info.txt'] = json.dumps(investor_info, ensure_ascii=False)

        change_info = self.change(task, entid)
        pages['change_info.txt'] = json.dumps(change_info, ensure_ascii=False)

        staff_info = self.staff(task, entid)
        pages['staff_info.txt'] = json.dumps(staff_info, ensure_ascii=False)

        return pages

    def investor(self, task: TJCrawlerTask, entid):
        url = "https://credit.scjg.tj.gov.cn/app_gsxt/system/label/getBasicByReginfoInvinfo"
        data = {"pageNum": 1, "pageSize": 1000,
                "searchParam": {"entid": entid}}
        res: dict = self.request(task, 'POST', url, json=data, name='investor', tojson=True)
        temp = []
        for i in res['rows']:
            temp.append({
                'inv': i['inv'],
                'blictype': i['blictype'],
                'blicno': i['blicno'],
                'invtype': i['invtype']
            })
        temp = ParseTools.unique(temp)
        return temp

    def change(self, task: TJCrawlerTask, entid):
        url = "https://credit.scjg.tj.gov.cn/app_gsxt/system/label/getBasicByReginfoEqupleReginfo"
        data = {"pageNum": 1, "pageSize": 1000,
                "searchParam": {"entid": entid}}
        res = self.request(task, 'POST', url, json=data, name='change', tojson=True)
        return res['rows']

    def staff(self, task: TJCrawlerTask, entid):
        url = "https://credit.scjg.tj.gov.cn/app_gsxt/perfinfo/getListdbcx"
        data = {
            "pageNum": 1, "pageSize": 1000,
            "searchParam": {
                "entid": entid, "moduleName": "other", "tableName": "T_GS_OTHER_PERSON"
            }
        }
        res: dict = self.request(task, 'POST', url, json=data, name='staff', tojson=True)
        return res['data']['主要人员信息']['rows']

    def search(self, task: TJCrawlerTask):
        data = {"isFuzzy": -1, "pageNum": 1, "pageSize": 5, "queryLikeStr": task.keyword, "gsdjlx": 1}
        res: dict = self.request(task, 'POST', self.search_url, json=data, name='search', tojson=True)
        list_ = res['data']['list']
        if not list_:
            raise MyException('搜索为空')
        url = "https://credit.scjg.tj.gov.cn/app_gsxt/system/label/getBasicByEntjbxx"
        data = {"entid": list_[0]['entid']}
        res = self.request(task, 'POST', url, json=data, name='detail', tojson=True)
        return res['data']
