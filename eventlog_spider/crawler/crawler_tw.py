import json
import re
from bs4 import BeautifulSoup
import pproxy
import asyncio, threading, time

from eventlog_spider.common.eventlog import EventlogN<PERSON> as Eventlog, SpiderCode
from eventlog_spider.crawler.crawler import Crawler, CrawlerTask, MyException, CrawlerTools
from resx.config import *
from resx.redis_types import RedisQueue
from resx.log import setup_logger

logger = setup_logger(name=__name__)


class TwCrawlerTask(CrawlerTask):

    def __init__(self, eventlog: Eventlog):
        super().__init__(eventlog)


class TwCrawler(Crawler, CrawlerTools):

    @classmethod
    def get_name(cls):
        return 'tw'

    def __init__(self, **kwargs):
        self.headers = {
            "Origin": "https://findbiz.nat.gov.tw",
            "Referer": "https://findbiz.nat.gov.tw/fts/query/QueryList/queryList.do",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0",
        }
        self.remote = pproxy.Connection(
            'ss://aes-128-gcm:94c2598d-a942-4386-817a-767c37974232'
            '@main-central.earthsurface.cc:45299'
        )
        self.proxies = {
            # 'http': 'socks5h://127.0.0.1:6666',
            # 'https': 'socks5h://127.0.0.1:6666',
            'http': 'http://127.0.0.1:7890',
            'https': 'http://127.0.0.1:7890',
        }
        threading.Thread(target=lambda: asyncio.run(start_local_proxy(self.remote)), daemon=True).start()
        time.sleep(1)
        self.timeout = 3
        super().__init__(input_queue=RedisQueue(name='tw', **CFG_REDIS_GS, db=9), task_cls=TwCrawlerTask, eventlog_class=Eventlog, **kwargs)

    def do_crawl(self):
        task: TwCrawlerTask = self.get_crawler_task()
        eventlog: Eventlog = task.eventlog

        if task.keyword is None:
            logger.warning(f'no keyword {eventlog}')
            eventlog.spider_code = SpiderCode.GIVE_UP
            task.pages = {}
            return

        try:
            task.pages = self.crawl_(task)
        except MyException as e:
            if e.message == '搜索为空':
                logger.warning(f'{task.keyword} -->搜索为空')
                raise MyException('11')
            if '连续失败' in e.message:
                logger.warning(f'连续失败 {eventlog}')
                raise MyException('1')
            task.pages = {}
            raise e
        except Exception as e:
            logger.error(CrawlerTools.custom_traceback(e))
            raise MyException('2')

    def crawl_(self, task: TwCrawlerTask):
        task.session.proxies = self.proxies
        url = "https://findbiz.nat.gov.tw/fts/query/QueryList/queryList.do"
        data = {
            "errorMsg": "",
            "validatorOpen": "N",
            "rlPermit": "0",
            "userResp": "",
            "curPage": "0",
            "fhl": "zh_TW",
            "qryCond": task.keyword,
            "infoType": "D",
            "qryType": "cmpyType",
            "cmpyType": "true",
            "brCmpyType": "",
            "busmType": "",
            "factType": "",
            "lmtdType": "",
            "isAlive": "all",
            "busiItemMain": "",
            "busiItemSub": ""
        }
        res = self.request(task, "POST", url, data=data)
        soup = BeautifulSoup(res, 'lxml')
        div = soup.select_one('div#vParagraph')
        a = div.select('a')
        if not a:
            raise MyException('搜索为空')
        logger.info('')
        params_ = re.search(r'objectId=(.*?)&banNo=(.*?)&disj=(.*?)&fhl=(.*)', a[0].get('href'), re.S)

        url = "https://findbiz.nat.gov.tw/fts/query/QueryCmpyDetail/queryCmpyDetail.do"
        params = {
            "objectId": params_.group(1).strip(),
            "banNo": params_.group(2),
            "disj": params_.group(3),
            "fhl": params_.group(4),
        }
        task.session.cookies.clear()
        res = self.request(task, 'POST', url, params=params)
        soup = BeautifulSoup(res, "lxml")
        table = soup.select('table.table.table-striped')
        if not table:
            raise MyException('1')
        trs = table[0].select('tbody > tr')

        # 基本信息
        result = {}
        for tr in trs:
            tds = tr.find_all('td')
            if len(tds) < 2:
                continue
            key = tds[0].get_text(strip=True)
            cell = tds[1]
            for sp in cell.find_all('span'):
                sp.decompose()
            value = cell.get_text(separator='', strip=True).replace('\xa0', '')
            result[key] = value

        # 董监事资料
        hdp = self.parse_table(table[2])
        # 经理人资料
        manager = self.parse_table(table[3])
        # 工厂资料
        factory = self.parse_table(table[5])

        return {
            'base_info.txt': json.dumps(result, ensure_ascii=False),
            'supervisor.txt': json.dumps(hdp, ensure_ascii=False),
            'manager.txt': json.dumps(manager, ensure_ascii=False),
            'factory.txt': json.dumps(factory, ensure_ascii=False),
        }

    @staticmethod
    def parse_table(table):
        headers = [th.get_text(strip=True) for th in table.thead.find_all('th')]
        result = []

        for tr in table.tbody.find_all('tr'):
            tds = tr.find_all('td')
            temp = {}
            if len(tds) == len(headers):
                for header, td in zip(headers, tds):
                    for sp in td.find_all('span'):
                        sp.decompose()
                    text = td.get_text(strip=True)
                    temp[header] = text
            result.append(temp)

        return result


async def start_local_proxy(remote):
    # 2. 本地监听 SOCKS5
    server = pproxy.Server('socks5://127.0.0.1:6666')
    args = {
        'rserver': [remote],  # 一组远端 Connection 列表
        'verbose': print  # 可选：日志回调
    }
    handler = await server.start_server(args)  # 异步启动代理服务
    try:
        await asyncio.Event().wait()  # 挂起当前协程
    finally:
        handler.close()


if __name__ == '__main__':
    pass
