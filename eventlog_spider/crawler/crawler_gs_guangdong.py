# -*- coding: UTF-8 -*-
"""
@Title   ：广东工商
<AUTHOR>
@Project ：gs_spider
@File    ：crawler_gs_guangdong.py
@Date    ：2025/2/10 19:05
"""
import random
import re
import math
import json
import base64
import time

import requests
from lxml import etree
from resx.redis_types import RedisQueue
from eventlog_spider.crawler.crawler import Crawler, CrawlerTask
from eventlog_spider.common.eventlog import EventlogOld as Eventlog, SpiderCode
from resx.config import *
from resx.log import setup_logger

logger = setup_logger(name=__name__)


class GSGuangDongCrawlerTask(CrawlerTask):
    def __init__(self, eventlog: Eventlog):
        self.session_init = requests.session()
        super().__init__(eventlog)


class GSGuangDongCrawler(Crawler):
    def __init__(self, **kwargs):
        self.PROXIES = {"http": "http://10.99.138.95:30636", "https": "http://10.99.138.95:30636"}
        self.HEADERS = {"User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36"}
        super().__init__(
            input_queue=RedisQueue(name='searchCompany_gdold_queue_updateOffline', **CFG_REDIS_GS, db=5),
            task_cls=GSGuangDongCrawlerTask,
            eventlog_class=Eventlog,
            **kwargs)

    @classmethod
    def get_name(cls):
        return 'gd'

    def do_crawl(self):
        """ 采集函数 """
        task = self.get_crawler_task()
        eventlog = task.eventlog
        pages = task.pages
        self.read_cache(eventlog)
        search_info = {
            "cache": task.crawl_cache,
            "company_id": eventlog.selector.word,
            "reg_num": eventlog.selector.get_info("reg_number"),
            "credit_no": eventlog.selector.get_info("credit_code"),
            "search_keyword": eventlog.selector.get_info("keyword"),
            "detail_url": eventlog.crawler.get("crawlerInfo").get("detailUrl") if eventlog.crawler.get(
                "crawlerInfo") else ""
        }
        # 广东深圳 PASS
        if (search_info["credit_no"] and search_info["credit_no"].startswith("9104403")) or \
                (search_info["reg_num"] and search_info["reg_num"].startswith("4403")):
            return

        # 采集函数
        status_code = self.crawl_process(search_info, pages)

        if status_code == 404:
            eventlog.spider_code = SpiderCode.SEARCH_EMPTY
            task.pages = {}
            task.crawl_cache = ""
        elif status_code == 500:
            eventlog.spider_code = SpiderCode.FAIL
            task.pages = {}
            task.crawl_cache = ""
        elif status_code == 200:
            ...
        else:
            eventlog.spider_code = SpiderCode.GIVE_UP
            task.pages = {}
            task.crawl_cache = ""

    def crawl_process(self, search_info: dict, pages: dict):
        """ 采集主函数 """
        if search_info["cache"]:
            # gs_spider 缓存
            url = search_info["cache"]["detail_url"]
            logger.info(f"[{search_info['credit_no']}]: URL来源: Cache")
        else:
            # 用户投词
            if search_info["detail_url"]:
                url = search_info["detail_url"]
                logger.info(f"[{search_info['credit_no']}]: URL来源: User")
            else:
                # 原java记录
                url = self.get_company_path_from_obs(search_info)
                if not url:
                    # 拼接
                    url = self.get_path_from_base_info(search_info)
                if not url:
                    # 新增 原gd html
                    url = self.get_path_from_company_gd(search_info)
                if not url:
                    # XA获取
                    url = self.get_path_from_xa(search_info)
        if url:
            if "szcredit" in url:
                return "shenzhen"
            if "gzaic" in url:
                return "guangzhou"

            url = url.replace("http", "https") if url.startswith("http") and not url.startswith("https") else url
            url = self.parser_user_url(url)
            search_info["cache"]["detail_url"] = url
            logger.info(f"[{search_info['credit_no']}]: URL= {url}")
        else:
            logger.warning(f"[{search_info['credit_no']}]: 未收录该公司")
            return 500

        if url:
            reg_org = url.split("regOrg=")[-1]
            ent_type = url.split("entType=")[-1].split("&regOrg")[0]
            ent_no = url.split("detail/")[-1].split("?entType")[0]
            logger.info(f"[{search_info['credit_no']}]: ent_no={ent_no},reg_org={reg_org}, ent_type={ent_type}")

            # 基本信息
            basic_code = self.crawl_basic_info(pages, url)
            if basic_code == 404:
                logger.warning(f"[{search_info['credit_no']}]: 未搜索到基本信息")
                return 404
            elif basic_code == 500:
                return 500
            logger.info(f"[{search_info['credit_no']}]: 基本信息采集完成")

            # 股东信息
            self.crawl_investors(pages, ent_no, reg_org)
            logger.info(f"[{search_info['credit_no']}]: 股东信息采集完成")

            # 主要人员
            self.crawl_staff(pages, ent_no, reg_org, ent_type)
            logger.info(f"[{search_info['credit_no']}]: 主要人员采集完成")

            # 变更记录
            self.crawl_change_info(pages, ent_no, reg_org)
            logger.info(f"[{search_info['credit_no']}]: 变更记录采集完成")

            # 经营异常
            self.crawl_abnormal_info(pages, ent_no, ent_type)
            logger.info(f"[{search_info['credit_no']}]: 经营异常采集完成")
            return 200
        return 404

    def check_company_name(self, basic_info):
        basic_info = basic_info.get("data", {})
        if not basic_info:
            return ""
        company_name = basic_info.get("entname")
        if not company_name:
            company_name = basic_info.get("traname")

        org_type = basic_info.get("entTypeCn")
        legal_person_name = basic_info.get("name")

        if org_type and company_name and ("个体" in org_type or "个人经营" in org_type) and (
                ("无字号" in company_name) or len(company_name) == 1 or not self.is_contains_chinese(company_name)) and \
                legal_person_name and not "null" == legal_person_name:
            return legal_person_name
        return company_name

    def crawl_basic_info(self, pages: dict, url: str):
        response = self.requests_retry("GET", url=url, headers=self.HEADERS)
        result = response.json() if response.status_code == 200 else {}
        company_name = self.check_company_name(result)
        if not company_name:
            return 404
        pages["basic_info.json"] = json.dumps(result, ensure_ascii=False) if result else {}
        if pages["basic_info.json"]:
            return 200
        else:
            return 500

    def crawl_investors(self, pages: dict, ent_no: str, reg_org: str):
        url = f"https://gsxt.amr.gd.gov.cn/gsxt_gd/ent/org/inv/list/{ent_no}?regOrg={reg_org}"
        # 不需要分页 json全部信息
        response = self.requests_retry("GET", url, self.HEADERS)
        result = response.json() if response.status_code == 200 else {}
        if result and result.get("data"):
            pages["investor_list_1.json"] = json.dumps(result, ensure_ascii=False)
            investor_list = result.get("data")

            # 股东详情出资
            if investor_list:
                for investor in investor_list:
                    inv_id = investor.get("invId")
                    self.crawl_investor_detail_info(pages, inv_id, ent_no, reg_org)

    def crawl_investor_detail_info(self, pages: dict, investor_no: str, ent_no: str, reg_org: str):
        url = f"https://gsxt.amr.gd.gov.cn/gsxt_gd/ent/org/inv/detail?entNo={ent_no}&regOrg={reg_org}" \
              f"&dataId={investor_no}"
        response = self.requests_retry("GET", url, self.HEADERS)
        result = response.json() if response.status_code == 200 else {}
        if result and result.get("data"):
            pages[f"investor_{investor_no}.json"] = json.dumps(result, ensure_ascii=False)

    def crawl_staff(self, pages: dict, ent_no: str, reg_org: str, ent_type: str):
        result = self.crawl_staff_more(1, ent_no, reg_org, ent_type)
        if result:
            pages[f"staff_list_1.json"] = json.dumps(result, ensure_ascii=False)
            page_total = math.ceil(int(result.get("data", {}).get("total", 0)) / 10)
            for _ in range(page_total-1):
                result = self.crawl_staff_more(_+2, ent_no, reg_org, ent_type)
                pages[f"staff_list_{_ + 2}.json"] = json.dumps(result, ensure_ascii=False)

    def crawl_staff_more(self, page_num: int, ent_no: str, reg_org: str, ent_type: str):
        url = "https://gsxt.amr.gd.gov.cn/gsxt_gd/ent/org/person/page"
        headers = {
            "Content-Type": "application/json",
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) "
                          "Chrome/133.0.0.0 Safari/537.36",
        }
        data_ = {
            "current": page_num,
            "size": 10,
            "entity": {
                "entNo": ent_no,
                "regOrg": reg_org,
                "entType": ent_type,
                "loadingBaseInfo": False
            }
        }
        data = json.dumps(data_, separators=(',', ':'))
        response = self.requests_retry("POST", url, headers=headers, data=data)
        result = response.json() if response.status_code == 200 else {}
        if result and result.get("data", {}).get("total") > 0:
            return result

    def crawl_change_info(self, pages: dict, ent_no: str, reg_org: str):
        result = self.crawl_change_info_more(1, ent_no, reg_org)
        if result:
            pages[f"change_list_1.json"] = json.dumps(result, ensure_ascii=False)
            page_total = math.ceil(int(result.get("data", {}).get("total", 0)) / 10)
            for _ in range(page_total - 1):
                result = self.crawl_change_info_more(1, ent_no, reg_org)
                pages[f"change_list_{_ + 2}.json"] = json.dumps(result, ensure_ascii=False)

    def crawl_change_info_more(self, page_num: int, ent_no: str, reg_org: str):
        url = "https://gsxt.amr.gd.gov.cn/gsxt_gd/ent/register/alter/page"
        headers = {
            "Content-Type": "application/json",
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) "
                          "Chrome/133.0.0.0 Safari/537.36",
        }
        data = {
            "current": page_num,
            "size": 10,
            "entity": {
                "entNo": ent_no,
                "regOrg": reg_org
            }
        }
        data = json.dumps(data, separators=(',', ':'))
        response = self.requests_retry("POST", url, headers=headers, data=data)
        result = response.json() if response.status_code == 200 else {}
        if result and result.get("data", {}).get("total") > 0:
            return result
        return None

    def crawl_abnormal_info(self, pages: dict, ent_no: str, ent_type: str):
        result = self.crawl_abnormal_info_more(1, ent_no, ent_type)
        if result:
            pages[f"abnormal_list_1.json"] = json.dumps(result, ensure_ascii=False)
            page_total = math.ceil(int(result.get("data", {}).get("total", 0)) / 10)
            for _ in range(page_total - 1):
                result = self.crawl_abnormal_info_more(1, ent_no, ent_type)
                pages[f"abnormal_list_{_ + 2}.json"] = json.dumps(result, ensure_ascii=False)

    def crawl_abnormal_info_more(self, page_num: int, ent_no: str, ent_type: str):
        url = "https://gsxt.amr.gd.gov.cn/gsxt_gd/ent/base/anomaly/page"
        headers = {
            "Content-Type": "application/json",
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36",
        }
        data = {
            "current": page_num,
            "size": 10,
            "entity": {
                "entNo": ent_no,
                "entType": ent_type
            }
        }
        data = json.dumps(data, separators=(',', ':'))
        response = self.requests_retry("POST", url, headers=headers, data=data)
        result = response.json() if response.status_code == 200 else {}
        if result and result.get("data", {}).get("total") > 0:
            return result
        return None

    def get_company_path_from_obs(self, search_info):
        company_url = self.obs_manager.download_pages(f"company/gd/{search_info['company_id']}/companyURL.txt")

        if not company_url.get("companyURL.txt"):
            return

        if company_url.get("companyURL.txt").startswith("http://gsxt.gdgs.gov.cn/GSpublicity/GSpublicityList.html"):
            return

        logger.info(f"[{search_info['credit_no']}]: URL来源: Java OBS")
        return company_url.get("companyURL.txt")

    def get_path_from_base_info(self, search_info):
        base_info = self.obs_manager.download_pages(f"company/gd/{search_info['company_id']}/baseInfo.json")
        if not base_info:
            return
        base_info = json.loads(base_info.get("baseInfo.json")) if base_info.get("baseInfo.json") else ""
        enttype = base_info.get("data", {}).get("enttype")
        if not enttype:
            if base_info.get("uniscid").startswith("92") or base_info.get("compformCn") == "个人":
                enttype = "9910"
            else:
                return ""
        url = "https://gsxt.amr.gd.gov.cn" + "/gsxt_gd/ent/base/detail/" + base_info.get("data", {}).get("pripid") + \
              "?entType=" + enttype + "&regOrg=" + base_info.get("data", {}).get("regorg")

        logger.info(f"[{search_info['credit_no']}]: URL来源: Java OBS Base Info")
        return url

    def get_path_from_company_gd(self, search_info):
        # 新增方法
        gd_content = self.obs_manager.download_pages(f"company/gd/{search_info['company_id']}/index.html")
        if not gd_content:
            return
        gd_content = gd_content["index.html"] if gd_content else ""

        url_map = self.parser_xa_html_find_new_url(gd_content)
        if url_map:
            logger.info(f"[{search_info['credit_no']}]: URL来源: Company GD OBS Index Html")
            return f"https://gsxt.amr.gd.gov.cn/gsxt_gd/ent/base/detail/{url_map['ent_no']}?entType={url_map['ent_type']}&regOrg={url_map['reg_org']}"
        return

    def get_path_from_xa(self, search_info: dict):
        # id中心获取 company_id
        company_id = 0
        # 1. credit_no
        headers = {'Authentication': 'OIfRTpvLVFWnFb6apSzmI2MRXGimQmVj', 'Content-Type': 'application/json'}
        data = json.dumps({"uniqueCode": search_info["credit_no"], "codeType": 1, "type": 2}, ensure_ascii=False)
        result = requests.post("http://idcenter-gsdata.jindidata.com/api/id-center/entity/v1", headers=headers,
                               data=data)
        if result.json().get("data", {}).get("best_id") != 0:
            company_id = result.json().get("data", {}).get("best_id")

        # 2. reg_num
        if company_id <= 0:
            data = json.dumps({"uniqueCode": search_info["reg_num"], "type": 2}, ensure_ascii=False)
            result = requests.post("http://idcenter-gsdata.jindidata.com/api/id-center/entity/v1", headers=headers,
                                   data=data)
            if result.json().get("data", {}).get("best_id") != 0:
                company_id = result.json().get("data", {}).get("best_id")

        # 3. key_word
        if company_id <= 0:
            if search_info["search_keyword"] and len(search_info["search_keyword"]) == 18 and \
                    search_info["search_keyword"].startswith("9"):
                data = json.dumps({"uniqueCode": search_info["search_keyword"], "codeType": 1, "type": 2},
                                  ensure_ascii=False)
                result = requests.post("http://idcenter-gsdata.jindidata.com/api/id-center/entity/v1", headers=headers,
                                       data=data)
                if result.json().get("data", {}).get("best_id") != 0:
                    company_id = result.json().get("data", {}).get("best_id")

        if company_id > 0:
            xa_path = f"company/xa/{company_id}/index.html"
            xa_content = self.obs_manager.download_pages(xa_path)
            if not xa_content:
                return
            xa_content = xa_content["index.html"] if xa_content else ""

            url_map = self.parser_xa_html(xa_content)
            if url_map.get("addNewHistoryScanUrl"):
                add_new_history_scan_url = url_map.get("addNewHistoryScanUrl")
                token = add_new_history_scan_url[
                        add_new_history_scan_url.find(".html") - 36:add_new_history_scan_url.find(".html")]
                if token.startswith("0000"):
                    token = token[4:]

                url = f"https://gsxt.amr.gd.gov.cn/gsxt_gd/ent/base/detail/{token}?entType=1&regOrg=441322"
                response = self.requests_retry("GET", url=url, headers=self.HEADERS)
                result = response.json() if response else {}
                if result and result.get("code") == 0:
                    url = f"https://gsxt.amr.gd.gov.cn/gsxt_gd/ent/base/detail/{token}?" \
                          f"entType={result['data']['enttype']}&regOrg={result['data']['regorg']}"

                logger.info(f"[{search_info['credit_no']}]: URL来源: XA")
                return url

    def parser_xa_html(self, xa_content: str):
        url_map = dict()
        xa_html = etree.HTML(xa_content)
        xa_divs = xa_html.xpath("//div[@id='url']")
        if not xa_divs:
            return url_map
        self.search_id_in_div(url_map, xa_divs, "entType")
        self.search_id_in_div(url_map, xa_divs, "nCaseFlag")
        self.search_id_in_div(url_map, xa_divs, "nLicFlag")
        url_element = xa_divs[0]
        add_new_history_scan_url = re.findall(r"addNewHistoryScanUrl = \"(.*)\";",
                                              "".join(url_element.xpath("//text()")))
        url_map["addNewHistoryScanUrl"] = add_new_history_scan_url[0] if add_new_history_scan_url else ""
        return url_map

    def parser_xa_html_find_new_url(self, xa_content: str):
        xa_html = etree.HTML(xa_content)
        ent_no = xa_html.xpath("//form//input[@id='entNo']/@value")[0] if xa_html.xpath("//form//input[@id='entNo']/@value") else ""
        ent_type = xa_html.xpath("//form//input[@id='entType']/@value")[0] if xa_html.xpath("//form//input[@id='entType']/@value") else ""
        reg_org = xa_html.xpath("//form//input[@id='regOrg']/@value")[0] if xa_html.xpath("//form//input[@id='regOrg']/@value") else ""
        url_map = {
            "ent_no": ent_no,
            "ent_type": ent_type,
            "reg_org": reg_org
        }
        if ent_no and ent_type and reg_org:
            return url_map
        return {}

    @staticmethod
    def parser_user_url(url):
        if "/ent/detail?key=" in url:
            json_key = {}
            key = url.split("key=")[1].replace("%3D", "=")
            if isinstance(base64.b64decode(key).decode("utf-8"), str):
                json_key = json.loads(base64.b64decode(key).decode("utf-8"))
            if json_key:
                if not key:
                    url = ""
                else:
                    url = f"https://gsxt.amr.gd.gov.cn/gsxt_gd/ent/base/detail/{json_key.get('entNo')}?" \
                          f"entType={json_key.get('entType')}&regOrg={json_key.get('regOrg')}"
                if "GSpublicity/GSpublicityList.html?service=" in url:
                    url = ""
        return url

    @staticmethod
    def search_id_in_div(url_map: dict, divs: list, name: str):
        for div in divs:
            value = div.xpath(f"//input[@id='{name}']/@value")
            if value:
                url_map[name] = value[0]

    @staticmethod
    def is_contains_chinese(text):
        """
        判断字符串是否包含中文
        :param text: 待判断的字符串
        :return: 如果包含中文返回 True，否则返回 False
        """
        for char in text:
            # 判断字符是否为中文字符
            if '\u4e00' <= char <= '\u9fff':
                return True
        return False

    def requests_retry(self, type_, url, headers, data=None, params=None, timeout=15):
        max_retries = 5
        for retry_count in range(max_retries):
            try:
                time.sleep(random.randint(200, 500) / 1000)
                request_kwargs = {
                    'url': url,
                    'headers': headers,
                    'proxies': self.PROXIES,
                    'data': data,
                    'params': params,
                    'timeout': timeout,
                    'verify': False
                }
                if type_.upper() == "GET":
                    response = requests.get(**request_kwargs)
                elif type_.upper() == "POST":
                    response = requests.post(**request_kwargs)
                else:
                    raise ValueError(f"不支持请求类型: {type_}")

                # 检查响应状态码
                if response.status_code == 200:
                    return response
                else:
                    logger.warning(f"请求失败 CODE:{response.status_code}. 重试 ({retry_count + 1}/{max_retries}). "
                                   f"URL: {url}, 响应文本: {response.text}, 参数: {data}")
            except Exception as e:
                logger.warning(f"请求错误: {e}. 重试 ({retry_count + 1}/{max_retries}). URL: {url}, 参数: {data}")
                continue
        raise Exception("超出单个请求重试次数")
