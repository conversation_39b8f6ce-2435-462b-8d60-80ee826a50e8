import json
import requests
from typing import Union
import time
import base64
import copy

from resx.redis_types import RedisQueue, Redis
from resx.config import *
from eventlog_spider.common.eventlog import EventlogOld as Eventlog, SpiderCode
from eventlog_spider.crawler.crawler import Crawler, CrawlerTask, MyException, CrawlerTools
from eventlog_spider.scripts.dianxuan.main import main_bj
from resx.log import setup_logger

logger = setup_logger(name=__name__)


class BeiJingCrawlerTask(CrawlerTask):

    def __init__(self, eventlog: Eventlog):
        self.validate_challenge = None
        self.get_new_session = False
        self.first = True
        super().__init__(eventlog)


class BeiJingCrawler(Crawler):
    @classmethod
    def get_name(cls):
        return 'bj'

    def __init__(self, **kwargs):
        self.headers = {
            'Host': 'qyxy.scjgj.beijing.gov.cn',
            'Accept': 'application/json',
            'Authorization': 'Basic Y3JlZGl0LWludGVybmV0OmludGVybmV0X3NlY3JldA==',
            'User-Agent': 'Mozilla/5.0 (Linux; Android 6.0.1; Nexus 6P Build/MTC20L; wv) AppleWebKit/537.36 (KHTML, '
                          'like Gecko) Version/4.0 Chrome/44.0.2403.117 Mobile Safari/537.36',
            'Accept-Language': 'zh-CN,en-US;q=0.8',
            'X-Requested-With': 'com.hyjx.main',
            'USE-RULE': 'true'
        }
        self.timeout = 3
        self.home = "http://qyxy.scjgj.beijing.gov.cn/home"
        self.register = 'http://qyxy.scjgj.beijing.gov.cn/server-api/gt/register'
        self.search_url = "http://qyxy.scjgj.beijing.gov.cn/server-api/front/entquery/entSearch"
        self.base_url = 'http://qyxy.scjgj.beijing.gov.cn/server-api/front/entquery/entInfo'
        self.url = "http://qyxy.scjgj.beijing.gov.cn/server-api/front/entInfo/pubInfo"
        self.inv_retail_url = "http://qyxy.scjgj.beijing.gov.cn/server-api/front/entInfo/pubCategDetailInfo"
        self.redis = Redis(**CFG_REDIS_GS, db=5)
        self.redis_hset_key = 'gs_bj_hset'
        self.data = {"sign": "", "signType": "", "token": ""}
        super().__init__(input_queue=RedisQueue(name='octopus_output_company_bj', **CFG_REDIS_GS, db=5), task_cls=BeiJingCrawlerTask, eventlog_class=Eventlog,
                         **kwargs)

    def do_crawl(self):
        task: BeiJingCrawlerTask = self.get_crawler_task()
        eventlog = task.eventlog

        if task.keyword is None:
            logger.warning(f'no keyword {eventlog}')
            eventlog.spider_code = SpiderCode.GIVE_UP
            task.pages = {}
            return

        try:
            task.pages = self.crawl_(task)
        except MyException as e:
            if e.message == '搜索为空':
                logger.warning(f'{task.keyword} - 搜索为空')
                eventlog.spider_code = SpiderCode.SEARCH_EMPTY
            if e.message == '连续错误':
                logger.warning(f'{task.keyword} - 连续错误')
                eventlog.spider_code = SpiderCode.FAIL
            task.pages = {}
        except Exception as e:
            raise e

    def crawl_(self, task: BeiJingCrawlerTask):
        pages = dict()
        pripid = self.get_cache_key(task.keyword)
        if not pripid:
            params = {"text": task.keyword, "pageNumber": "0", "pageSize": "10"}
            search_result: dict = self.request(task, 'POST', self.search_url, params=params, json=self.data, name='search', tojson=True, gee=True)
            content = search_result['data']['content']
            if not content:
                raise MyException('搜索为空')
            if content[0]['entname'] == '***':
                raise MyException('搜索为空')
            pripid = content[0]['pripid']
            self.set_cache_key(task.keyword, pripid)

        # 仅获取注册号
        company_info_: dict = self.request(task, 'POST', self.base_url, params={'id': pripid}, json=self.data, name='company_info_', tojson=True, gee=True)
        company_info_: dict = company_info_.get('data')
        regno = company_info_.get('regno', '')
        uniscid = company_info_.get('uniscid', '')

        params = {"infotype": "010001", "menucode": "01E01E01", "entid": pripid, "pageInfoType": "010001", "pageSize": "200", "pageNum": "0", }
        company_info: list = self.request(task, 'GET', self.url, params=params, name='company_info', tojson=True, gee=True).get('data').get('data')
        company_info[1]['fieldvalue'] = uniscid
        company_info = [c['fieldvalue'] for c in company_info]
        company_info.append(regno)

        params.update({"infotype": "010002", "menucode": "01E01E02", "pageInfoType": "010002", })  # "menucode": "01E14E02", # 合伙
        investor_info: list = self.request(task, 'GET', self.url, params=params, name='investor_info', tojson=True, gee=True).get('data').get('data')

        # if '合伙' in company_info[0]:
        params_ = copy.deepcopy(params)
        del params_['infotype']
        del params_['menucode']
        del params_['entid']
        params_['entId'] = pripid
        for inv in investor_info:
            params_.update({'dataId': inv['invid'], 'subMenuCode': inv['subMenuCode']})
            inv_retail_info: list = self.request(task, 'GET', self.inv_retail_url, params=params_, name='inv_retail_info', tojson=True, gee=True).get('data')
            inv['认缴额（万元）'] = inv_retail_info[0]['data'][0].get('lisubconam', '')
            inv['实缴额（万元）'] = inv_retail_info[0]['data'][0].get('liacconam', '')
            # inv['认缴'] = inv_retail_info[1]['data'] if inv_retail_info[1:2] else []
            inv['认缴'] = []
            # inv['实缴'] = inv_retail_info[2:][0]['data'] if inv_retail_info[2:] else []
            inv['实缴'] = []

        params.update({"infotype": "010003", "menucode": "01E01E03", "pageInfoType": "010003"})
        staff_info: list = self.request(task, 'GET', self.url, params=params, name='staff_info', tojson=True, gee=True).get('data').get('data')

        params.update({"infotype": "010007", "menucode": "01E00E07", "pageInfoType": "010007"})
        change_info: list = self.request(task, 'GET', self.url, params=params, name='change_info', tojson=True, gee=True).get('data').get('data')

        pages['company_info.txt'] = json.dumps(company_info, ensure_ascii=False)
        pages['investor_info.txt'] = json.dumps(investor_info, ensure_ascii=False)
        pages['staff_info.txt'] = json.dumps(staff_info, ensure_ascii=False)
        pages['change_info.txt'] = json.dumps(change_info, ensure_ascii=False)
        return pages

    def get_validate(self, task: BeiJingCrawlerTask):
        url = "http://10.99.193.106:8778/get_validate_v3"
        res = None
        for _ in range(4):
            try:
                gt_challenge = self.get_gt_challenge(task)
                validate, _ = main_bj(gt_challenge)

                # data = {
                #     "gt": gt_challenge['gt'],
                #     "challenge": gt_challenge['challenge'],
                #     "refer": self.home
                # }
                # res = requests.post(url, json=data, timeout=20)
                # if (res.status_code in [400, 500]) or ('validate' not in res.json()):
                #     logger.warning(f'获取validate{res.text} 重试')
                #     continue

                # return res.json()
                return {
                    'validate': validate,
                    "challenge": gt_challenge['challenge']
                }

            # except requests.exceptions.Timeout:
            #     logger.warning('获取validate超时 重试')
            #     continue
            except MyException as e:
                raise e
            except Exception as e:
                logger.error(f'get_validate --> {e}')
                continue
        raise MyException('连续错误')

    def get_gt_challenge(self, task: BeiJingCrawlerTask):
        gt_challenge = ''
        for i in range(4):
            if gt_challenge == '重试' or task.get_new_session or task.first:
                task.first = False
                task.get_new_session = False
                task.session.cookies.clear()
                res = self.request(task, 'GET', self.home, name='home')

            gt_challenge = self.request(task, 'GET', self.register, params={'t': int(time.time() * 1000)}, name='register', tojson=True)
            if gt_challenge == '重试':
                logger.warning(base64.b64decode(task.session.cookies["proxyBase"]).decode())
                logger.warning('获取gt_challenge重试')
                continue
            else:
                return gt_challenge
        raise MyException('连续错误')

    def request(self, task: BeiJingCrawlerTask, method: str, url: str, params: dict = None, data: dict = None,
                json: dict = None, headers: dict = None, path: str = None, name: str = '', tojson=False, gee=False) -> Union[dict, str]:
        for i in range(5):
            if gee:
                if not task.validate_challenge:
                    task.validate_challenge = self.get_validate(task)
                params.update({"geetest_challenge": task.validate_challenge['challenge'],
                               "geetest_validate": task.validate_challenge['validate'],
                               "geetest_seccode": f"{task.validate_challenge['validate']}{'|'}jordan"})
            response = None
            try:
                response = task.session.request(**{'method': method, 'url': url, 'data': data, 'headers': headers if headers else self.headers,
                                                   'verify': False, 'timeout': self.timeout, 'params': params, 'json': json})
                status = response.status_code

                if status in [206, 500]:
                    logger.warning(f'{name} --> continue status：{response.status_code} res: {response.content.decode("utf-8")}')
                    del task.session.cookies['proxyBase']
                    continue

                logger.info(f'{name} --> {response.status_code}')
                a = response.content.decode('utf-8').replace("\n", "").replace('\r', '').replace('\t', '')
                if "请完成验证" in a:
                    logger.warning(f'{name} --> 人机校验界面')
                else:
                    logger.info(f'{name} --> {a[:300]}')
                if tojson:
                    if 'msg' in response.json() and response.json()['msg'] == '极验校验失败':
                        task.validate_challenge = None
                        continue
                    return response.json()
                return response.content.decode("utf-8")
            except (requests.exceptions.ConnectionError, requests.exceptions.Timeout) as e:
                logger.warning(f'{name} --> continue reason: {e.__class__.__name__}')
                del task.session.cookies['proxyBase']
            except requests.exceptions.JSONDecodeError:
                if name != 'register':
                    logger.warning('重定向到了人机验证')
                    task.validate_challenge = None
                    task.get_new_session = True
                    continue
                return '重试'
            except Exception as e:
                status = response.status_code if response else ""
                text = response.content.decode('utf-8') if response else ""
                logger.warning(f'{name} --> continue status: {status} res: {text} reason: {CrawlerTools.custom_traceback(e)}')
                del task.session.cookies['proxyBase']
        raise MyException('连续错误')

    def get_cache_key(self, key):
        cache = self.redis.hget(self.redis_hset_key, key)
        if isinstance(cache, str):
            return cache
        if isinstance(cache, bytes):
            return cache.decode('utf8')
        return None

    def set_cache_key(self, key, value):
        self.redis.hset(self.redis_hset_key, key, value)
