import base64
import json
import re
import cv2
import numpy as np
from Crypto.Cipher import PKCS1_v1_5
from Crypto.PublicKey import RSA

from eventlog_spider.common.eventlog import EventlogNew as Eventlog, SpiderCode
from eventlog_spider.crawler.crawler import <PERSON><PERSON><PERSON>, CrawlerTask, MyException, CrawlerTools
from resx.config import *
from resx.redis_types import Redis, RedisQueue
from resx.log import setup_logger

logger = setup_logger(name=__name__)


class CrawlerNpoTask(CrawlerTask):
    pass


class CrawlerNpo(Crawler, CrawlerTools):
    @classmethod
    def get_name(cls):
        return 'china_npo'

    def __init__(self, **kwargs):
        self.timeout = 3
        self.headers = {
            "Origin": "https://xxgs.chinanpo.mca.gov.cn",
            "Referer": "https://xxgs.chinanpo.mca.gov.cn/gsxt/newDetails?b=eyJpZCI6IjUxMTEwMDAwTUowMTAwOTMyNSJ9",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* "
                          "Safari/537.36",
        }
        self.redis = Redis(**CFG_REDIS_GS, db=5)
        super().__init__(input_queue=RedisQueue(name='npo', **CFG_REDIS_GS, db=9), task_cls=CrawlerNpoTask, eventlog_class=Eventlog, **kwargs)

    def do_crawl(self):
        task: CrawlerNpoTask = self.get_crawler_task()
        task.pages = {}
        eventlog: Eventlog = task.eventlog

        if task.keyword is None:
            logger.warning(f'no keyword {eventlog}')
            eventlog.spider_code = SpiderCode.GIVE_UP

        try:
            task.pages = self.crawl_(task)
        except MyException as e:
            if e.message == '搜索为空':
                logger.warning(f'{task.keyword} --> 搜索为空')
                self.redis.sadd('npo.empty', task.keyword)
                eventlog.spider_code = SpiderCode.SEARCH_EMPTY
            if e.message == '接口连续失败':
                logger.warning(f'{task.keyword} --> 接口连续失败')
                eventlog.spider_code = SpiderCode.FAIL
        except Exception as e:
            raise e

    def crawl_(self, task: CrawlerNpoTask):
        if re.match(r'[\u4e00-\u9fa5]', task.keyword):
            task.keyword = self.slideCaptcha(task)
            if not task.keyword:
                task.eventlog.spider_code = SpiderCode.FAIL
                return

        data = {'id': task.keyword}
        url = 'https://xxgs.chinanpo.mca.gov.cn/gsxt/PlatformSHZZFRKGSXT/biz/ma/shzzgsxt/a/getAae01CertificateInfo.html'
        info1: dict = self.request(task, 'POST', url, json=data, tojson=True, name=f'{task.keyword}-登记证书信息-json')['result']
        if not info1:
            raise MyException('搜索为空')
        url = 'https://xxgs.chinanpo.mca.gov.cn/gsxt/PlatformSHZZFRKGSXT/biz/ma/shzzgsxt/a/getAae01Info.html'
        info2 = self.request(task, 'POST', url, json=data, tojson=True, name=f'{task.keyword}-状态信息-json')['result']
        info1.update(info2)

        url = "https://xxgs.chinanpo.mca.gov.cn/gsxt/PlatformSHZZFRKGSXT/biz/ma/shzzgsxt/a/getValueGrid.html"
        # 活动异常
        # data = {"pageNo": 1, "pageSize": 10, "id": task.keyword, "name": "aae20"}
        # activity_info: dict = self.request(task, 'POST', url, json=data, tojson=True, name=f'{task.keyword}-活动异常-json')

        # 严重违法
        # data = {"pageNo": 1, "pageSize": 10, "id": task.keyword, "name": "aae21"}
        # illegal_info: dict = self.request(task, 'POST', url, json=data, tojson=True, name=f'{task.keyword}-严重违法-json')

        data = {"pageNo": 1, "pageSize": 50, "id": task.keyword, "name": "aae30"}
        change_info: dict = self.request(task, 'POST', url, json=data, tojson=True, name=f'{task.keyword}-变更信息-json')

        data = {"pageNo": 1, "pageSize": 10, "id": task.keyword, "name": "aae12"}
        year_check_info: dict = self.request(task, 'POST', url, json=data, tojson=True, name=f'{task.keyword}-年检-json')

        data = {"pageNo": 1, "pageSize": 10, "id": task.keyword, "name": "aae22"}
        commend_info: dict = self.request(task, 'POST', url, json=data, tojson=True, name=f'{task.keyword}-表彰信息-json')

        data = {"pageNo": 1, "pageSize": 10, "id": task.keyword, "name": "aae14"}
        evaluate_info: dict = self.request(task, 'POST', url, json=data, tojson=True, name=f'{task.keyword}-评估信息-json')

        return {
            'registration_certificate_info.json': json.dumps(info1, ensure_ascii=False),
            # 'activity_info.json': json.dumps(activity_info['result']['data'], ensure_ascii=False),
            'change_info.json': json.dumps(change_info['result']['data'], ensure_ascii=False),
            'year_check_info.json': json.dumps(year_check_info['result']['data'], ensure_ascii=False),
            'commend_info.json': json.dumps(commend_info['result']['data'], ensure_ascii=False),
            'evaluate_info.json': json.dumps(evaluate_info['result']['data'], ensure_ascii=False),
        }

    def slideCaptcha(self, task):
        url = 'https://xxgs.chinanpo.mca.gov.cn/gsxt/PlatformSHZZFRKGSXT/slideCaptcha'
        image: dict = self.request(task, 'GET', url, tojson=True, name=f'{task.keyword}-slideCaptcha')
        move_x = self.Captcha(image['result']['c']['oriImage'])

        params = {'a': base64.b64encode(image['result']['a'].encode('utf-8')).decode(),
                  'b': base64.b64encode(image['result']['b'].encode('utf-8')).decode(),
                  'c': base64.b64encode(self.RSA_Encrypt(str(move_x)).encode('utf-8')).decode()}
        url = 'https://xxgs.chinanpo.mca.gov.cn/gsxt/PlatformSHZZFRKGSXT/slide_captcha_check'
        res = self.request(task, 'GET', url, params=params, tojson=True, name=f'{task.keyword}-slide_captcha_check')
        if res['msg'] != 'success':
            return False
        data = {'pageNo': 1, 'pageSize': 10, 'paramsValue': task.keyword, 'ssfw': '1', 'aaae0127': '', 'xyzk': '', 'aaae0129': '', 'aaae0105': '',
                'aaae0123': '', 'aaae0114': '', 'aae15having': '', 'aaae0145': '', 'aaae0110': '', 'aaae0137': '', 'aaae0149': '', 'aaae0136': '',
                'aaae0139': ''}
        data.update(params)
        url = 'https://xxgs.chinanpo.mca.gov.cn/gsxt/PlatformSHZZFRKGSXT/biz/ma/shzzgsxt/a/gridQuery.html'
        res: dict = self.request(task, 'POST', url, json=data, tojson=True, name=f'{task.keyword}-gridQuery')
        return res['result']['data'][0]['aaae0102']

    @staticmethod
    def Captcha(base64_str: str):

        base64_data = base64.b64decode(base64_str)
        # 将二进制数据转换成numpy数组
        np_data = np.frombuffer(base64_data, np.uint8)
        # 读取图片并进行解码
        img = cv2.imdecode(np_data, cv2.IMREAD_COLOR)
        hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
        # 设置提取白色的阈值范围
        lower_white = np.array([0, 0, 255])  # 白色的最低阈值
        upper_white = np.array([180, 30, 255])  # 白色的最高阈值
        # 根据阈值范围创建掩膜
        mask = cv2.inRange(hsv, lower_white, upper_white)
        # 将掩膜应用于原始图像
        result = cv2.bitwise_and(img, img, mask=mask)
        # 将其他颜色置为黑色
        result[np.where((result != [255, 255, 255]).all(axis=2))] = [0, 0, 0]

        k = np.ones((10, 10), np.uint8)
        opening = cv2.morphologyEx(result, cv2.MORPH_OPEN, k)

        opening = cv2.Canny(opening, 100, 200)
        contours, _ = cv2.findContours(opening, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        for contour in contours:
            x, y, w, h = cv2.boundingRect(contour)
            cv2.rectangle(img, (x, y), (x + w, y + h), (0, 255, 0), 2)
            return x

    @staticmethod
    def RSA_Encrypt(text: str):
        public_key = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCCsYUGHMhjSzdMqn9JzPfKs9JbxXTPtHofTv7reV0HrEz4brnE6ZJpNn5s934KO3L4QDF7ELHysIiounhhpF1bewW9jKdcpZA5M1CkGHKcwpLA2liaqOlt/0Mf3ui9jxR9AHxUMFVGfJ6Q4+cEmDBUAEOXlxqk4ZjGpubwGNk9XQIDAQAB"
        public_key_str = f'-----BEGIN PUBLIC KEY-----\n{public_key}\n-----END PUBLIC KEY-----\n'
        key = RSA.import_key(public_key_str)
        cipher = PKCS1_v1_5.new(key)
        encrypt_text = base64.b64encode(cipher.encrypt(str(text).encode())).decode()
        return encrypt_text
