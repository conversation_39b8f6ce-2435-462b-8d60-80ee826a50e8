from crawler_log.log_init_v2 import getLogger

logger = getLogger(__name__)
import re
import json
import requests
from typing import Dict, Union
from requests import Session

from resx.redis_types import RedisQueue, Redis
from resx.config import *
from eventlog_spider.common.eventlog import EventlogOld as Eventlog, SpiderCode
from eventlog_spider.crawler.crawler import <PERSON>rawler, CrawlerTask, MyException, CrawlerTools


class ChengduCrawlerTask(CrawlerTask):
    def __init__(self, eventlog: Eventlog):
        self.rs = {
            'url_md5': '702fe0350467a083f86d02fe90268687',
            'new': True
        }
        super().__init__(eventlog)


class ChengduCrawler(<PERSON><PERSON><PERSON>, CrawlerTools):
    @classmethod
    def get_name(cls):
        return 'sccd'

    def __init__(self, **kwargs):
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) "
                          "Chrome/********* Safari/537.36"
        }
        self.timeout = 20
        self.address = '127.0.0.1:9222'
        super().__init__(input_queue=RedisQueue(name='octopus_output_company_sccd', **CFG_REDIS_GS, db=5), task_cls=ChengduCrawlerTask, eventlog_class=Eventlog,
                         **kwargs)

    def do_crawl(self):
        task: ChengduCrawlerTask = self.get_crawler_task()
        eventlog = task.eventlog

        if not task.keyword:
            logger.warning(f'no keyword {eventlog}')
            eventlog.spider_code = SpiderCode.GIVE_UP
            task.pages = {}
            return

        try:
            task.pages = self.crawl_(task)
        except MyException as e:
            if e.message == '连续失败':
                eventlog.spider_code = SpiderCode.FAIL
            if e.message == '搜索无结果':
                eventlog.spider_code = SpiderCode.SEARCH_EMPTY
            task.pages = {}
        except Exception as e:
            raise e

    def crawl_(self, task: ChengduCrawlerTask) -> Dict[str, str]:
        pages = {}
        url = "https://credit.chengdu.gov.cn/search/getRecommendation.do"
        data = {"text": task.keyword, "unit": "0", "appType": "APP001"}
        res: dict = self.request(task, 'POST', url, data=data, name='search', tojson=True)
        search_params = res['msg']['rows']

        if not search_params:
            raise MyException('搜索无结果')

        url = "https://credit.chengdu.gov.cn/EPBaseInfo/getGsjRegisterDetail.do"
        data = {"unit": "1", "appType": "APP001", "accpNo": search_params[0]['accpNo'], "id": search_params[0]['id'], "authorization": ""}
        res: dict = self.request(task, 'POST', url, data=data, name='base_info', tojson=True)
        base_info = {item['title']: item['text'] for item in res['msg']['base'][0]['items'][0]}

        url = "https://credit.chengdu.gov.cn/EPBaseInfo/getEpBaseInfo.do"
        data = {"id": search_params[0]['id'],
                "accpNo": search_params[0]['accpNo'],
                "regNo": search_params[0]['regNo'],
                "name": base_info['主体名称'],
                "appType": "APP001", "unit": "1", "unitType": "1"}
        res: dict = self.request(task, 'POST', url, data=data, name='base_info2', tojson=True)
        base_info.update({item['title']: item['text'] for item in res['msg']['base']['items']})
        del res['msg']['base']['items']
        base_info.update(res['msg']['base'])

        url = "https://credit.chengdu.gov.cn/EPBaseInfo/getOperateManageDetail.do"
        data = {"regNo": search_params[0]['regNo'], "unit": "1", "appType": "APP001", "accpNo": search_params[0]['accpNo'],
                "id": search_params[0]['id'], "authorization": ""}
        res: dict = self.request(task, 'POST', url, data=data, name='staff_investor', tojson=True)
        staff_info, investor_info = [], []
        if res['msg']['base']:
            for i in res['msg']['base']:
                if i['name'] == '主要人员信息':
                    staff_info = [{'name': item[0]['text'], 'position': item[1]['text']} for item in i['items']]
                if i['name'] in ['投资人信息', '合伙人信息']:
                    investor_info = [{item2['title']: item2['text'] for item2 in item}
                                     for item in i['items']]

        logger.info(base_info)
        logger.info(staff_info)
        logger.info(investor_info)
        pages['base_info.txt'] = json.dumps(base_info, ensure_ascii=False)
        pages['staff_info.txt'] = json.dumps(staff_info, ensure_ascii=False)
        pages['investor_info.txt'] = json.dumps(investor_info, ensure_ascii=False)
        return pages

    def request(self, session_or_task: Union[Session, CrawlerTask], method: str, url: str, params: dict = None, data: Union[dict, str] = None,
                json: dict = None, path: str = None, name: str = '', tojson=False, toRaw=False, long_proxy=False, long_timeout=30, isDetail=False) -> \
            Union[dict, str, requests.Response]:
        task: ChengduCrawlerTask = session_or_task
        for i in range(10):
            response = None
            try:
                self.get_cookies(task)
                response = task.session.request(**{'method': method, 'url': url, 'data': data, 'headers': self.headers,
                                                   'verify': False, 'timeout': self.timeout, 'params': params, 'json': json})
                status = response.status_code
                if status == 412:
                    logger.warning(f'412 获取cookies')
                    task.rs.update({'html': response.text})
                    continue
                if status != 200:
                    logger.warning(f'{name} {i} --> {status}')
                    del task.session.cookies['proxyBase']
                    continue

                if name not in ['']:
                    logger.info(f'{name} --> {response.status_code}')
                    a = re.sub(r'[\r\n\t]', '', response.text)
                    logger.info(f'{name} --> {a}')

                if toRaw:
                    return response
                if tojson:
                    return response.json()
                return response.text
            except (requests.exceptions.ConnectionError, requests.exceptions.Timeout) as e:
                logger.warning(f'continue{i} exception: {e}')
                del task.session.cookies['proxyBase']
                continue
            except Exception as e:
                status = response.status_code if response else "空"
                text = response.text if response else "空"
                logger.warning(f'continue-{i} 状态码：{status} res: {text} exception: {e}')
                del task.session.cookies['proxyBase']
                continue
        raise MyException('接口连续失败')

    @staticmethod
    def get_cookies(task: ChengduCrawlerTask):
        if 'html' in task.rs:
            res = requests.post('http://10.99.199.241:19081/get_cookie', data=task.rs, timeout=5)
            task.session.cookies.set('fyEsJd77deyzP', res.json()['cookie'], domain='credit.chengdu.gov.cn')
