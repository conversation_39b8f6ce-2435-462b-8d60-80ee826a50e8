import re
import json
from typing import Dict, Optional
import ddddocr
from bs4 import BeautifulSoup

from resx.redis_types import RedisQueue, Redis
from resx.config import *
from eventlog_spider.common.eventlog import EventlogOld as Eventlog, SpiderCode
from eventlog_spider.crawler.crawler import <PERSON><PERSON>ler, CrawlerTask, MyException, CrawlerTools
from crawler_log.log_init_v2 import getLogger

logger = getLogger(__name__)


class ShanghaiCrawlerTask(CrawlerTask):
    pass


class ShanghaiCrawler(Crawler, CrawlerTools):
    @classmethod
    def get_name(cls):
        return 'sh'

    def __init__(self, **kwargs):
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) "
                          "Chrome/119.0.0.0 Safari/537.36"
        }
        self.ocr = ddddocr.DdddOcr(show_ad=False)
        self.timeout = 5
        super().__init__(input_queue=RedisQueue(name='gsxt_sh_crawler_input', **CFG_REDIS_GS, db=5), task_cls=ShanghaiCrawlerTask,
                         eventlog_class=Eventlog, **kwargs)

    def do_crawl(self):
        task: ShanghaiCrawlerTask = self.get_crawler_task()
        eventlog = task.eventlog

        if not task.keyword:
            logger.warning(f'no keyword {eventlog}')
            eventlog.spider_code = SpiderCode.GIVE_UP
            task.pages = {}
            return

        try:
            task.pages = self.crawl_(task)
        except MyException as e:
            if e.message == '连续失败':
                eventlog.spider_code = SpiderCode.FAIL
            if e.message == '搜索无结果':
                eventlog.spider_code = SpiderCode.SEARCH_EMPTY
            task.pages = {}
        except Exception as e:
            raise e

    def crawl_(self, task: ShanghaiCrawlerTask) -> Dict[str, str]:
        pages = dict()
        self.request(task, 'get', "https://fw.scjgj.sh.gov.cn/achieve_out/fgQuery.jsp")
        etps_ids = []
        retry_count = 0
        for i in range(5):
            try:
                res2 = self.request(task, 'get', "https://fw.scjgj.sh.gov.cn/achieve_out/vFCodeServlet", toRaw=True)
                verify = self.ocr.classification(res2.content)
                url = "https://fw.scjgj.sh.gov.cn/achieve_out/fgEtpsList.jsp"
                data = {"name": task.keyword.encode('gbk'), "reg_no": "", "unisc_id": "", "vfcode": verify}
                response = self.request(task, 'post', url, data=data)
                soup = BeautifulSoup(response, 'lxml')
                trs = soup.select('tr td a')

                retry_count += 1
                if not trs:
                    if retry_count <= 3:
                        continue
                    raise MyException('搜索无结果')

                # 名称匹配
                a = []
                for j in trs:
                    if task.keyword == j.text:
                        a.append(j)

                if not a:
                    raise MyException('搜索无结果')

                etps_ids = []
                for a_ in a:
                    etps_id = re.search(r"check\('(.*?)'", a_.get('onclick')).group(1)
                    etps_ids.append(etps_id)
                logger.info(etps_ids)
                break
            except Exception as e:
                self.custom_traceback(e)
                logger.info(f'验证码错误，重试 {e}')
                continue

        if not etps_ids:
            raise MyException('连续失败')

        info, investor = {}, []
        for etps_id in etps_ids:
            response2 = self.request(task, 'post', "https://fw.scjgj.sh.gov.cn/achieve_out/fgEtpsInfo.jsp",
                                     data={"etps_id": etps_id, "applier_type": "", "pageNo": "1"})
            info, investor = self.paser(response2)
            if info.get('统一社会信用代码') == task.eventlog.selector.info.get('credit_code', ''):
                break

        if not info:
            raise MyException('搜索无结果')

        pages['base_info'] = json.dumps(info)
        pages['investor'] = json.dumps(investor[1:])
        return pages

    @staticmethod
    def paser(html):
        soup = BeautifulSoup(html, 'lxml')
        trs = soup.find_all('tr')
        b = {}
        index = None
        for idx, tr in enumerate(trs):
            if index:
                break
            a: list = list(tr.stripped_strings)
            if not a:
                continue
            if a[0] in ['登记事项', '曾用名']:
                continue
            if a[0] == '登记机关':
                index = idx
            if a[0] == '经营范围':
                a[1] = re.sub('(\r|\t|\s|\n|【.*】)', '', a[1])
            if a[0] == '统一社会信用代码' and (not a[1:2]):
                a.append(None)
            if len(a) != 2:
                continue
            b.update({a[0]: a[1]})

        t = list(trs[index + 1].stripped_strings)
        if (t[0] if t else '') == '隶属企业':
            index += 1

        investor = []
        if trs[index + 1:]:
            for tr in trs[index + 1:]:
                a = list(tr.stripped_strings)
                if len(a) >= 2:
                    investor = a
                    break

        logger.info(investor)
        logger.info(b)
        return b, investor
