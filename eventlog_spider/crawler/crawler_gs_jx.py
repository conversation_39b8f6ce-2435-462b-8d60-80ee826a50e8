# encoding=utf8
import re
import json
import time
import base64
from bs4 import BeautifulSoup
import requests

from resx.redis_types import RedisQueue
from resx.config import *
from eventlog_spider.common.eventlog import EventlogOld as Eventlog, SpiderCode
from eventlog_spider.crawler.crawler import <PERSON><PERSON><PERSON>, CrawlerTask, MyException, CrawlerTools
from resx.log import setup_logger

logger = setup_logger(name=__name__)


class GSJXCrawlerTask(CrawlerTask):
    def __init__(self, eventlog: Eventlog):
        self.search = eventlog.selector.get_info('keyword')
        super().__init__(eventlog)


class GSJXCrawler(<PERSON><PERSON>ler, CrawlerTools):
    def __init__(self, **kwargs):
        self.headers = {
            'Host': 'gsxt.amr.jiangxi.gov.cn',
            "Referer": "https://gsxt.amr.jiangxi.gov.cn/",
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0',
            'X-Requested-With': 'XMLHttpRequest'
        }
        self.timeout = 5
        self.search_name_url = "https://gsxt.amr.jiangxi.gov.cn/vfygeettest/querygeetest.do"
        self.search_url = "https://gsxt.amr.jiangxi.gov.cn/search/querysearch.do"
        self.base_info_url = "https://gsxt.amr.jiangxi.gov.cn/baseinfo/queryenterpriseinfoByRegnore.do"
        self.staff_info_url = "https://gsxt.amr.jiangxi.gov.cn/epriperson/queryPerson.do"
        self.investor_info_url = "https://gsxt.amr.jiangxi.gov.cn/einvperson/getqueryeInvPersonService.do"
        self.change_info_url = "https://gsxt.amr.jiangxi.gov.cn/gtalterrecoder/getquerygtalterrecoder.do"
        self.capital_info_url = "https://gsxt.amr.jiangxi.gov.cn/einvperson/queryInfo.do"

        super().__init__(input_queue=RedisQueue(name='octopus_output_company_jx', **CFG_REDIS_GS, db=5), task_cls=GSJXCrawlerTask, eventlog_class=Eventlog,
                         **kwargs)

    @classmethod
    def get_name(cls):
        return 'jx'

    def crawl_(self, task: GSJXCrawlerTask):
        pages = dict()

        params = self.get_params(task)
        search_url = self.request(task, 'POST', self.search_name_url, data=params, name='search_url', tojson=True)['url']
        name = re.findall(r'ename=(.*)&liketype', search_url)[0]
        company_info = self.request(task, 'POST', self.search_url, params={'name': name}, name='company_info', tojson=True)
        if not company_info['data']:
            raise MyException('搜索为空')
        company_info: dict = company_info['data'][0]
        pripid = self.encode(company_info['PRIPID'])

        base_info = self.request(task, 'GET', self.base_info_url, params={
            'pripid': pripid,
            '_': str(int(time.time() * 1000))
        }, name='base_info', tojson=True)

        staff_info = self.request(task, 'GET', self.staff_info_url, params={
            'pripid': pripid,
            '_': str(int(time.time() * 1000))
        }, name='staff_info', tojson=True)

        investor_info = self.request(task, 'GET', self.investor_info_url, params={
            'pripid': pripid,
            '_': str(int(time.time() * 1000)),
            "pageIndex": "0",
            "pageSize": "500",
        }, name="investor_info", tojson=True)['data']

        for investor in investor_info:
            investor: dict
            invid = investor['INVID']
            capital_html = self.request(task, 'GET', self.capital_info_url, params={'invid': invid}, name='capital_html')
            capital_dict = self.parse_html(capital_html)
            investor['认缴'] = capital_dict['认缴']
            investor['实缴'] = capital_dict['实缴']

        change_info = self.request(task, 'GET', self.change_info_url, params={
            'pripid': pripid,
            '_': str(int(time.time() * 1000)),
            "pageIndex": "0",
            "pageSize": "500",
        }, name="change_info", tojson=True)['data']

        pages['base_info.txt'] = json.dumps(base_info, ensure_ascii=False)
        pages['staff_info.txt'] = json.dumps(staff_info, ensure_ascii=False)
        pages['investor_info.txt'] = json.dumps(investor_info, ensure_ascii=False)
        pages['change_info.txt'] = json.dumps(change_info, ensure_ascii=False)
        return pages

    def do_crawl(self):
        task: GSJXCrawlerTask = self.get_crawler_task()
        eventlog = task.eventlog
        if task.search is None:
            logger.warning(f'no keyword {eventlog}')
            eventlog.spider_code = SpiderCode.GIVE_UP
            task.pages = {}
            return
        try:
            task.pages = self.crawl_(task)
        except MyException as e:
            if e.message == '搜索为空':
                logger.debug(f'搜索为空')
                eventlog.spider_code = SpiderCode.SEARCH_EMPTY
            if '连续失败' in e.message:
                logger.debug(f'连续失败')
                eventlog.spider_code = SpiderCode.FAIL
            task.pages = {}
            return
        except Exception as e:
            raise e

    def get_gt_challenge(self, task: GSJXCrawlerTask):
        url = "https://gsxt.amr.jiangxi.gov.cn/start/querygeetest.do"
        params = {"v": str(int(time.time() * 1000))}
        res = self.request(task, 'GET', url, params=params, name='geetest_validate', tojson=True)
        return res

    def get_params(self, task: GSJXCrawlerTask):
        url = 'http://10.99.206.19:8889/getValidateOffline'
        for i in range(3):
            try:
                gee = self.get_gt_challenge(task)
                data = {'challenge': gee['challenge'], 'prov': 'jx'}
                res = requests.get(url, params=data)
                return {'geetest_challenge': gee['challenge'],
                        'geetest_validate': res.text,
                        'geetest_seccode': res.text + '|jordan',
                        'searchkey': self.encode(task.search),
                        'searchtype': 'qyxy',
                        'entname': task.search}

            except Exception as e:
                logger.warning('极验重试')
                continue
        raise MyException('极验接口连续失败3次')

    @staticmethod
    def encode(text: str) -> str:
        encode_text = base64.b64encode(text.encode()).decode('utf-8')
        encode_text = encode_text.replace("+", "u002B")
        encode_text = encode_text.replace("=", "u002C")
        encode_text = encode_text.replace("/", "u002D")
        encode_text = encode_text.replace("\n", "").replace("\r", "")
        return encode_text

    @staticmethod
    def parse_html(html: str):
        soup = BeautifulSoup(html, 'lxml')
        tables = soup.find_all('table')
        capital_info = {}
        for idx, table in enumerate(tables[1:]):
            trs = table.find_all('tr')
            capitals = []
            for tr in trs[1:]:
                info = {}
                for idx2, td in enumerate(tr.find_all('td')):
                    td_str = list(td.stripped_strings)
                    td_str = td_str[0] if len(td_str) > 0 else ''
                    if idx2 == 0:
                        info['出资方式'] = td_str
                    elif idx2 == 1:
                        info['出资额（万元）'] = td_str
                    elif idx2 == 2:
                        info['出资日期'] = td_str
                capitals.append({
                    'amomon': info['出资额（万元）'],
                    'time': info['出资日期'].replace('年', '-').replace('月', '-').replace('日', ''),
                    'paymet': '货币'
                })
            if idx == 0:
                capital_info.update({'认缴': capitals})
            else:
                capital_info.update({'实缴': capitals})
        return capital_info
