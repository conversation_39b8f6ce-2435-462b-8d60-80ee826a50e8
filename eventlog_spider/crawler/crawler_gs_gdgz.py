import json
import ddddocr
import re
import random
from bs4 import BeautifulSoup

from resx.redis_types import RedisQueue, Redis
from resx.config import *
from eventlog_spider.common.eventlog import EventlogOld as Eventlog, SpiderCode
from eventlog_spider.crawler.crawler import C<PERSON>ler, CrawlerTask, MyException, CrawlerTools
from crawler_log.log_init_v2 import getLogger

logger = getLogger(__name__)


class GDGZCrawlerTask(CrawlerTask):
    pass


class GDGZCrawler(Crawler, CrawlerTools):
    @classmethod
    def get_name(cls):
        return 'gdgz'

    def __init__(self, **kwargs):
        self.headers = {
            "Origin": "https://portal.scjgj.gz.gov.cn",
            "Referer": "https://portal.scjgj.gz.gov.cn/aiccips/GZpublicity/toSearch",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        }
        self.timeout = 5
        self.ocr = ddddocr.DdddOcr(show_ad=False)
        super().__init__(input_queue=RedisQueue(name='octopus_output_company_gdgz', **CFG_REDIS_GS, db=5), task_cls=GDGZCrawlerTask, eventlog_class=Eventlog,
                         **kwargs)

    def do_crawl(self):
        task: GDGZCrawlerTask = self.get_crawler_task()
        eventlog = task.eventlog

        if task.keyword is None:
            logger.warning(f'no keyword {eventlog}')
            eventlog.spider_code = SpiderCode.GIVE_UP
            task.pages = {}
            return

        try:
            task.pages = self.crawl_(task)
        except MyException as e:
            if e.message == '搜索为空':
                logger.warning(f'搜索为空')
                eventlog.spider_code = SpiderCode.SEARCH_EMPTY
            if '连续失败' in e.message:
                logger.warning(f'{e.message}')
                eventlog.spider_code = SpiderCode.FAIL
            task.pages = {}
        except Exception as e:
            raise e

    def crawl_(self, task: GDGZCrawlerTask):
        pages = {}
        captcha = self.pass_capcha(task)
        url = "https://portal.scjgj.gz.gov.cn/aiccips/GZpublicity/showEnt"
        data = {"entName": "", "regNo": "", "uniSCID": task.keyword, "code": captcha, "exactFlag": "0"}

        entNo = None
        for _ in range(3):
            response = self.request(task, 'POST', url, data=data, name='获取entNo参数')
            entNo = re.search(r'GZpublicity/GZpublicityList\?entNo=(.*?)&regOrg=&service=entInfo&code=', response)
            if entNo:
                break
        if not entNo:
            raise MyException('搜索为空')

        entNo = entNo.group(1)
        logger.info(f'entNo: {entNo}')

        captcha = self.pass_capcha(task)

        url = "https://portal.scjgj.gz.gov.cn/aiccips/GZpublicity/GZpublicityList"
        params = {"entNo": entNo, "regOrg": "", "service": "entInfo", "code": captcha}
        response = self.request(task, 'GET', url, params=params, name='商事登记基本信息')

        soup = BeautifulSoup(response, 'lxml')
        trs = soup.select('tr')
        key, value = [], []
        for tr in trs:
            list_ = list(tr.stripped_strings)
            key.extend(list_[0::2])
            value.extend(list_[1::2])
        data = dict(zip(key, value))
        logger.info(data)

        pages['base_info.txt'] = json.dumps(data, ensure_ascii=False)
        return pages

    def pass_capcha(self, task: GDGZCrawlerTask):
        captcha = ''
        for _ in range(5):
            url = "https://portal.scjgj.gz.gov.cn/aiccips/VerifyCode/getCode"
            params = {"random": str(round(random.random(), 16))}
            response = self.request(task, 'GET', url, params=params, name='获取验证码图片', toRaw=True)
            captcha = self.ocr.classification(response.content)
            logger.info(f"captcha: {captcha}")

            url = "https://portal.scjgj.gz.gov.cn/aiccips/VerifyCode/checkCodeGz"
            params = {"entName": "", "regNo": "", "text3": task.keyword, "code": captcha}
            res: dict = self.request(task, 'GET', url, params=params, name='check_code', tojson=True)
            logger.info(res)
            if res['flag'] == '1':
                break
        if not captcha:
            raise MyException('验证码连续失败')
        return captcha
