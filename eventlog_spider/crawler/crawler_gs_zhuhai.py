from typing import Optional, Union
from urllib import parse
import time
import random
import json
import re
from requests import Response

from resx.redis_types import RedisQueue
from resx.config import *
from eventlog_spider.common.eventlog import EventlogOld as Eventlog, SpiderCode
from eventlog_spider.crawler.crawler import Crawler, CrawlerTask, MyException, CrawlerTools
from resx.log import setup_logger

logger = setup_logger(name=__name__)


class ZhuhaiCrawlerTask(CrawlerTask):
    pass


class ZhuhaiCrawler(Crawler, CrawlerTools):
    @classmethod
    def get_name(cls):
        return 'gdzh'

    def __init__(self, **kwargs):
        self.timeout = 5
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.55 Safari/537.36'
        }
        super().__init__(input_queue=RedisQueue(name='octopus_output_company_gdzh', **CFG_REDIS_GS, db=5), task_cls=ZhuhaiCrawlerTask, eventlog_class=Eventlog,
                         **kwargs)

    def do_crawl(self):
        task: ZhuhaiCrawlerTask = self.get_crawler_task()
        eventlog = task.eventlog

        if task.keyword is None:
            logger.warning(f'no keyword {eventlog}')
            eventlog.spider_code = SpiderCode.GIVE_UP
            task.pages = {}
            return

        try:
            task.pages = self.crawl_(task)
        except MyException as e:
            if e.message == '搜索为空':
                logger.warning(f'搜索为空')
                eventlog.spider_code = SpiderCode.SEARCH_EMPTY
            if e.message == '接口连续失败':
                logger.warning(f'接口连续失败')
                eventlog.spider_code = SpiderCode.FAIL
            task.pages = {}
        except Exception as e:
            raise e

    def crawl_(self, task: ZhuhaiCrawlerTask):
        pages = {}
        search_data = self.get_search_data(task)

        zch = search_data['SCZTBH']
        uid = search_data['TYSHXYDM']
        key = search_data['QYMC']
        info_types = {"JBXX": 'base_info', 'GDXX': 'investor_info', 'GGRY': 'staff_info', 'BGXX': 'change_info'}
        detail_refer = 'https://ssgs.zhuhai.gov.cn/modes/QYXX.html?zch={}&tyxydm={}&keywords={}&v={}'
        refer = detail_refer.format(zch, uid, key, random.random())

        for k, v in info_types.items():
            url = f'https://ssgs.zhuhai.gov.cn/modes/qyxx.aspx?qtype={k}&e={int(time.time() * 1000)}'
            data = {'PI': 1, 'RC': 200, 'zch': zch} if k != 'GGRY' else {'zch': zch}
            pages[v + ".txt"] = self.get_message_from_api(task, url, data, refer, v)

        pages['base_info.txt'] = self.parse_base(pages)
        # 判断公司类型 是否需要存入主要人员
        if self.parse_staff(pages):
            pages['staff_info.txt'] = json.dumps(pages['staff_info.txt'], ensure_ascii=False)
        else:
            del pages['staff_info.txt']
        # 将值以str存储
        pages['base_info.txt'] = json.dumps(pages['base_info.txt'], ensure_ascii=False)
        pages['investor_info.txt'] = json.dumps(pages['investor_info.txt'], ensure_ascii=False)
        pages['change_info.txt'] = json.dumps(pages['change_info.txt'], ensure_ascii=False)
        return pages

    def get_message_from_api(self, task: ZhuhaiCrawlerTask, url, data, refer, name):
        detail_headers = {'Host': 'ssgs.zhuhai.gov.cn',
                          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.55 Safari/537.36',
                          'Content-Type': 'application/x-www-form-urlencoded', 'Accept': '*/*',
                          'Origin': 'https://ssgs.zhuhai.gov.cn', 'Referer': parse.quote(refer),
                          'Accept-Encoding': 'gzip, deflate', 'Accept-Language': 'zh-CN,zh;q=0.9'}
        res: dict = self.request(task, 'POST', url, data=data, name=name, tojson=True)
        return res['Table']

    def get_search_data(self, task: ZhuhaiCrawlerTask) -> Optional[dict]:
        query_data = {"searchtype": "ent_tab", "keyword": task.keyword, "cStatus": "0", "eYear": "0",
                      "filter": "0", "queryData": "queryData", "page": "1", "pageSize": "10"}
        url = "https://ssgs.zhuhai.gov.cn/modes/qylb.aspx"
        res: Response = self.request(task, 'POST', url, data=query_data, name='search', toRaw=True)
        if 'text/html' in res.headers.get('content-type'):
            raise MyException('搜索为空')
        return res.json()['Table'][0]

    @staticmethod
    def parse_base(crawled_item: dict) -> dict:
        table = crawled_item['base_info.txt'][0]
        company = {
            'base': 'gd',
            'name': table.get('QYMC', ''),
            'legal_person_id': '',
            'legal_person_name': table.get('FDDBR', ''),
            'legal_person_type': '',
            'company_org_type': table.get('ZTLX', ''),
            'reg_location': table.get('JYDZ', ''),
            'estiblish_time': table.get('QYCLRQ', '').replace('T', ' '),
            'reg_number': table.get('ZCH', ''),
            'from_time': table.get('YYQSRQ', '').replace('T', ' '),
            'to_time': table.get('YYJZRQ', '').replace('T', ' ') if table.get('YYJZRQ', '') else '',
            'business_scope': table.get('JYFW', '') or '',
            'reg_institute': table.get('DJJG', ''),
            'reg_status': table.get('DJZTMC', ''),
            'credit_code': table.get('TYSHXYDM', ''),
            'approved_time': table.get('HZRQ', '').replace('T', ' '),
            'reg_capital': table.get('ZCZB', ''),
            'actual_capital': '',
            "company_type": 0,
        }
        if company['reg_capital'] is not None:
            unit = {
                "个体工商户": '万元'
            }.get(table.get('ZTDL', ''), "万")
            if table.get('ZCBZ', '') == "人民币" or table.get('ZCBZ') is None:
                currency = ""
            elif table.get('ZCBZ'):
                currency = table.get('ZCBZ')
            company['reg_capital'] = str(company['reg_capital']) + unit + currency
        else:
            company['reg_capital'] = ''
        if "委派代表:" in company['legal_person_name']:
            pattern = r'\(.*?\)'  # 匹配括号及其内部内容
            result = re.sub(pattern, '', company['legal_person_name'])
            company['legal_person_name'] = result
        return company

    @staticmethod
    def parse_staff(pages):
        base_info = pages['base_info.txt']
        company_org_type = base_info['company_org_type']
        if "合伙" in company_org_type:
            return False
        return True
