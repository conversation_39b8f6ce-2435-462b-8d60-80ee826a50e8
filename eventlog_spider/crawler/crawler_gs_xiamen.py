import re
import json
from typing import Dict
from urllib.parse import urlencode
import ddddocr
from bs4 import BeautifulSoup
import time
import PIL

from resx.redis_types import RedisQueue
from eventlog_spider.common.eventlog import EventlogOld as Eventlog, SpiderCode
from eventlog_spider.crawler.crawler import <PERSON><PERSON><PERSON>, CrawlerTask, MyException, CrawlerTools
from resx.log import setup_logger
from resx.config import *

logger = setup_logger(name=__name__)


class XiamenCrawler(<PERSON><PERSON><PERSON>, CrawlerTools):
    @classmethod
    def get_name(cls):
        return 'fjxm'

    def __init__(self, **kwargs):
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 "
                          "Safari/537.36"
        }
        self.timeout = 15
        self.ocr = ddddocr.DdddOcr(show_ad=False)
        super().__init__(input_queue=RedisQueue(name='octopus_output_company_fjxm', **CFG_REDIS_GS, db=5), task_cls=CrawlerTask, eventlog_class=Eventlog,
                         **kwargs)

    def do_crawl(self):
        task = self.get_crawler_task()
        eventlog = task.eventlog

        if task.keyword is None:
            logger.warning(f'no keyword {eventlog}')
            eventlog.spider_code = SpiderCode.GIVE_UP
            task.pages = {}
            return

        try:
            task.pages = self.crawl_(task)
        except MyException as e:
            if '连续失败' in e.message:
                logger.warning(f'连续失败 {eventlog}')
                eventlog.spider_code = SpiderCode.FAIL
            if e.message == '搜索为空':
                logger.warning(f'搜索为空 {eventlog}')
                eventlog.spider_code = SpiderCode.SEARCH_EMPTY
            task.pages = {}
        except PIL.UnidentifiedImageError:
            logger.warning('网站挂了')
            time.sleep(60 * 2)
        except Exception as e:
            raise e

    def crawl_(self, task: CrawlerTask) -> Dict[str, str]:
        pages = dict()
        self.query(task)

        base_html = self.request(task, 'get', "https://credit.scjg.xm.gov.cn:8085/xmgsggfw/jsp/ssztcx/tab_1_0.jsp", name='base_html')
        investor_html = self.request(task, 'get', "https://credit.scjg.xm.gov.cn:8085/xmgsggfw/jsp/ssztcx/tab_2_1.jsp", name='investor_html')
        staff_html = self.request(task, 'get', "https://credit.scjg.xm.gov.cn:8085/xmgsggfw/jsp/ssztcx/tab_3_1.jsp", name='staff_html')

        base_info = self.parse_html2(base_html)
        investor_info = self.parse_html(investor_html)
        staff_info = self.parse_html(staff_html)
        logger.info(base_info)
        logger.info(investor_info)
        logger.info(staff_info)
        pages['base_info.txt'] = json.dumps(base_info, ensure_ascii=False)
        pages['investor_info.txt'] = json.dumps(investor_info, ensure_ascii=False)
        pages['staff_info.txt'] = json.dumps(staff_info, ensure_ascii=False)
        return pages

    def query(self, task: CrawlerTask):
        url = "https://credit.scjg.xm.gov.cn:8085/xmgsggfw/query.jsp"
        # * 获取session_id
        params = {"cxjk": "Sszt", "imgurl": "tatlet-sssearch", "title": self.encode_str('商事主体公示查询'), "zch": "", "qymc": "", "keyword": ""}
        self.request(task, 'get', url, params=params, name='获取session_id')

        self.headers.update({
            "Referer": 'https://credit.scjg.xm.gov.cn:8085/xmgsggfw/query.jsp?' + urlencode(params),
            "Origin": "https://credit.scjg.xm.gov.cn:8085"})

        self.pass_captcha(task)

        # * 搜索
        url4 = "https://credit.scjg.xm.gov.cn:8085/xmgsggfw/jsp/ssztcx/ssztQueryList.jsp"
        response2 = self.request(task, 'get', url4, name='搜索')

        soup = BeautifulSoup(response2, 'lxml')
        tr = soup.find_all('tr')
        info = re.findall(r'javascript:ssztView\((.*)\)', tr[1].contents[3].a.get('href'))[0].split(',')
        info = [i.replace("'", '') for i in info]

        ulr_params = [
            {
                "action": "qyjbxx",
                "url": "tab_1_0",
                "qyid": info[0],
                "datatype": "",
                "pdivid": "qyjbxxDiv"
            }, {
                "action": "tzrxx",
                "url": "tab_2_1",
                "qyid": info[0],
                "pdivid": "tzrxxDiv"
            }, {
                "action": "zyryxx",
                "url": "tab_3_1",
                "qyid": info[0],
                "pdivid": "zyryxxDiv"
            }
        ]
        for i in ulr_params:
            self.request(task, 'get', "https://credit.scjg.xm.gov.cn:8085/xmgsggfw/SsztServlet", params=i)

    def pass_captcha(self, task: CrawlerTask):
        for i in range(5):
            # * 获取pic
            url2 = "https://credit.scjg.xm.gov.cn:8085/xmgsggfw/image.jsp"
            params2 = {"RandomCodeUrl": "SsztValidateCode"}
            res2 = self.request(task, 'get', url2, params=params2, toRaw=True, name='获取pic')
            # * 识别验证码
            verify_result = self.ocr.classification(res2.content)
            # * 验证
            url3 = "https://credit.scjg.xm.gov.cn:8085/xmgsggfw/SsztServlet"
            params3 = {"action": "querySszt", "validate": "true"}
            data = {"sfzmqqy": "null", "zch": "", "qymc": "", "keyword": "", "cxz": "商事主体", "validateCode": verify_result}
            if self.contains_chinese(task.keyword):
                data.update({"qymc": task.keyword})
            else:
                data.update({"zch": task.keyword})

            res = self.request(task, 'post', url3, params=params3, data=data, name='验证')

            if res == '1':
                logger.warning('验证码错误 重试')
                continue
            elif res == 'ssztQueryList':
                return
            elif res == "3":
                raise MyException('搜索为空')

        raise MyException('验证码连续失败')

    @staticmethod
    def encode_str(str_: str):
        return str_.encode('unicode-escape').decode('utf-8').replace('\\', '%')

    @staticmethod
    def parse_html(html):
        def deal_str(str_):
            return str_.string.strip() if str_.string else ''

        soup = BeautifulSoup(html, 'lxml')
        trs = soup.find_all('tr')
        list_ = [[deal_str(i) for i in tr.contents if
                  str(type(i)) != "<class 'bs4.element.Comment'>" and i != '\n']
                 for tr in trs]

        if not list_:
            return []

        a = list_.pop(0)
        b = [dict(zip(a, i)) for i in list_]
        return b

    @staticmethod
    def parse_html2(html):
        soup = BeautifulSoup(html, 'lxml')
        trs = soup.find_all('tr')
        base_info = {tr.contents[1].string.strip()[:-1]: list(tr.contents[3].strings)[0].strip() for tr in trs if
                     list(tr.contents[3].strings) != []}
        return base_info
