# -*- coding: UTF-8 -*-
"""
@Title   ：黑龙江工商
<AUTHOR>
@Project ：gs_spider
@File    ：crawl_heilongjiang.py
@Date    ：2025/2/8 09:33
"""
import re
import time
import requests
from lxml import etree
from eventlog_spider.common.eventlog import EventlogOld as Eventlog, SpiderCode
from eventlog_spider.crawler.crawler import Crawler, CrawlerTask
from resx.redis_types import RedisQueue
from resx.config import *
from resx.log import setup_logger

logger = setup_logger(name=__name__)


class GSHeiLongJiangCrawlerTask(CrawlerTask):
    def __init__(self, eventlog: Eventlog):
        self.search = eventlog.selector.get_info('keyword')
        self.session_init = requests.session()
        super().__init__(eventlog)

class GSHeiLongJiangCrawler(Crawler):
    def __init__(self, **kwargs):
        self.PROXIES = {"http": "http://10.99.138.95:30636","https": "http://10.99.138.95:30636"}
        self.HEADERS = {"User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36"}
        super().__init__(
            input_queue=RedisQueue(name='searchCompany_hljold_queue_updateOffline', **CFG_REDIS_GS, db=5),
            task_cls=GSHeiLongJiangCrawlerTask,
            eventlog_class=Eventlog,
            **kwargs)

    @classmethod
    def get_name(cls):
        return 'hlj'

    def do_crawl(self):
        """ 运行采集函数 """
        task = self.get_crawler_task()
        eventlog = task.eventlog
        pages = task.pages
        self.read_cache(eventlog)
        search_word, session_init = task.search, task.session_init
        status_code = self.crawl_process(pages, task.crawl_cache, search_word, session_init)

        if status_code == 500:
            eventlog.spider_code = SpiderCode.FAIL
            task.pages = {}
        elif status_code == 404:
            eventlog.spider_code = SpiderCode.SEARCH_EMPTY
            task.pages = {}
        elif status_code == 200:
            ...
        else:
            eventlog.spider_code = SpiderCode.GIVE_UP
            task.pages = {}

    @staticmethod
    def check_data_label_for_company(html: str, search_keyword: str):
        old_html = etree.HTML(html)
        credit_no = old_html.xpath("//td[contains(text(), '统一社会信用代码')]/span/text()")
        reg_no = old_html.xpath("//td[contains(text(), '注册号')]/span/text()")
        company_name = old_html.xpath("//td[contains(text(), '企业名称')]/span/text()")

        if not credit_no and not reg_no and not company_name:
            credit_no = old_html.xpath("//div[@id='basic_center']//span[@class='label'][0]/text()")
            company_name = old_html.xpath("//div[@id='basic_center']//span[@id='entName']/text()")
        if credit_no or reg_no or company_name:
            infos_ = list()
            infos_.extend([credit_no_item.strip() for credit_no_item in credit_no])
            infos_.extend([reg_no_item.strip() for reg_no_item in reg_no])
            infos_.extend([company_name_item.strip() for company_name_item in company_name])
            if search_keyword in infos_:
                logger.info(f"[{search_keyword}]: 命中data_label, 采用过去的data_label进行采集")
                return True
        return False

    def crawl_process(self, pages, cache, search_keyword, session_init):
        # # 有 data_label 的直接采集
        if cache:
            data_label = cache["data_label"]
            detail_html = self.search_company_detail(session_init, data_label)

            # 检查 data_label 是否变动
            is_not_change = self.check_data_label_for_company(detail_html, search_keyword)
            # 如果没有变 直接跳过验证搜索阶段
            if is_not_change:
                pages["detail.html"] = detail_html
                logger.info(f"[{search_keyword}]: details={True if '核准日期' in detail_html else False}")

                # 获取详情页2
                detail_html2 = self.search_company_detail2(session_init, data_label)
                if detail_html2:
                    pages["detail2.html"] = detail_html2

                self.non_primary_dimension(session_init, data_label, pages, search_keyword)
                return 200
            else:
                logger.warning(f"[{search_keyword}]: 详情页地址变动 重新抓取更新详情页地址")
                session_init = requests.session()

        # 获取 gt, challenge
        gt_challenge_result = self.gt_challenge(session_init)
        gt = gt_challenge_result["gt"]
        challenge = gt_challenge_result["challenge"]
        logger.info(f"[{search_keyword}]: gt={gt}, challenge={challenge}")

        # 生成validate
        validate = self.prd_validate(search_keyword, gt, challenge).get("validate")
        validate = validate.split(",")[1] if validate else 301
        if isinstance(validate, int):
            return validate
        logger.info(f"[{search_keyword}]: validate={validate}")

        # 验证validate,获取搜索列表地址
        search_json = self.verify_validate(session_init, search_keyword, challenge, validate)
        logger.info(f"[{search_keyword}]: search_json={search_json}")

        # 获取列表页
        search_list_html = self.search_company_list(session_init, search_json["obj"], search_keyword)
        logger.info(f"[{search_keyword}]: search_list_html={bool(search_list_html)}")

        # 解析列表页 拿到匹配公司 data_label
        data_label = self.parser_search_list(search_list_html, search_keyword)
        if data_label == 404:
            logger.warning(f"[{search_keyword}]: 搜索无结果")
            return 404
        elif data_label == 500:
            return 500
        cache["data_label"] = data_label
        logger.info(f"[{search_keyword}]: data_label={data_label}")

        # 获取详情页
        detail_html = self.search_company_detail(session_init, data_label)
        pages["detail.html"] = detail_html
        logger.info(f"[{search_keyword}]: details={True if '核准日期' in detail_html else False}")

        # 获取详情页2
        detail_html2 = self.search_company_detail2(session_init, data_label)
        if detail_html2:
            pages["detail2.html"] = detail_html2

        # 获取 股东人员翻页; 股东详细信息; 主要人员翻页; 变更记录翻页;
        self.non_primary_dimension(session_init, data_label, pages, search_keyword)
        return 200

    def non_primary_dimension(self, session_init, data_label, pages, search_keyword):
        """ 股东人员翻页; 股东详细信息; 主要人员翻页; 变更记录翻页; """
        # 股东信息
        investor_html = self.search_investor_list(session_init, data_label, "1")
        investor_page_all = 0
        if investor_html:
            pages["investor_list_1.html"] = investor_html
            investor_page_all = self.is_more_page(investor_html)
            if investor_page_all > 1:
                for page_number in range(1, investor_page_all):
                    investor_html = self.search_investor_list(session_init, data_label, f"{page_number + 1}")
                    pages[f"investor_list_{page_number + 1}.html"] = investor_html

        # 股东详细信息
        if investor_page_all > 0:
            # 获取每个股东id
            for page in range(0, investor_page_all):
                investor_ids = self.parser_investor_detail(pages[f"investor_list_{page + 1}.html"])
                for investor_id in investor_ids:
                    pages[f"investor_info_{investor_id}.html"] = self.search_investor_detail(session_init, investor_id)
        logger.info(f"[{search_keyword}]: 股东共{investor_page_all}页, 已采集完成详细信息")

        # 主要人员
        staff_html = self.search_staff_list(session_init, data_label)
        if staff_html:
            pages["staff_list.html"] = staff_html
        logger.info(f"[{search_keyword}]: 主要人员, 已采集完成详细信息")

        # 变更记录
        change_html = self.search_change_list(session_init, data_label, "1")
        change_page_all = 0
        if change_html:
            pages["change_list_1.html"] = change_html
            change_page_all = self.is_more_page(change_html)
            if change_page_all > 1:
                for page_number in range(1, change_page_all):
                    investor_html = self.search_change_list(session_init, data_label, f"{page_number + 1}")
                    pages[f"change_list_{page_number + 1}.html"] = investor_html
        logger.info(f"[{search_keyword}]: 变更记录共{change_page_all}页, 已采集完成详细信息")

    def search_change_list(self, session_init, data_label, page):
        detail_url = "https://gsxt.hlj.gov.cn/business/QueryAltList.jspx"
        params = {
            "pno": f"{page}",
            "order": "0",
            "mainId": f"{data_label}",
        }
        response = self.session_retry(session_init, type_="GET", url=detail_url, headers=self.HEADERS, params=params)
        return response.text

    def search_staff_list(self, session_init, data_label):
        detail_url = "https://gsxt.hlj.gov.cn/business/loadMoreMainStaff.jspx"
        params = {
            "uuid": f"{data_label}",
            "order": "1",
        }
        response = self.session_retry(session_init, type_="GET", url=detail_url, headers=self.HEADERS, params=params)
        return response.text

    def search_investor_detail(self, session_init, inv_id):
        detail_url = "https://gsxt.hlj.gov.cn/checkInvCapInfo.jspx"
        params = {
            "invId": f"{inv_id}",
            "date": f"{int(time.time() * 1000)}"
        }
        response = self.session_retry(session_init, type_="GET", url=detail_url, headers=self.HEADERS, params=params)
        if response.text == "yes":
            detail_url = "https://gsxt.hlj.gov.cn/queryInvDetailAction.jspx"
            params = {
                "invId": f"{inv_id}"
            }
            response = self.session_retry(session_init, type_="GET", url=detail_url, headers=self.HEADERS, params=params)
        return response.text

    @staticmethod
    def parser_investor_detail(html):
        investor_ids = re.findall(r"seeInvest\('(.*?)'", html)
        return investor_ids

    @staticmethod
    def is_more_page(html_first):
        num = re.findall("共&nbsp;(\d*)&nbsp;页", html_first, re.DOTALL)
        num = int(num[0]) if num else 0
        return num

    def search_investor_list(self, session_init, data_label, page):
        detail_url = "https://gsxt.hlj.gov.cn/business/QueryInvList.jspx"
        params = {
            "pno": f"{page}",
            "order": "0",
            "mainId": f"{data_label}",
        }
        response = self.session_retry(session_init, type_="GET", url=detail_url, headers=self.HEADERS, params=params)
        return response.text

    def search_company_list(self, session_init, status, search_word):
        search_url = f"https://gsxt.hlj.gov.cn/{status}&searchType=1&entName={search_word}"
        response = self.session_retry(session_init, "GET", url=search_url, headers=self.HEADERS)
        return response.text

    @staticmethod
    def parser_search_list(search_list_html, search_word):
        etree_html = etree.HTML(search_list_html)
        company_items = etree_html.xpath("//div[@id='gggscpnamebox']")  # 获取本页所有公司
        search_num_info = ''.join(etree_html.xpath("//div[@id='searchtipsu1']//p//span//text()"))  # 获取信息
        if "0条" in search_num_info:
            return 404

        # 匹配 命中keyword的企业(名字或者统代)
        for company in company_items:
            name = "".join(company.xpath(".//p[@class='gggscpnametitle']/span[@class='qiyeEntName']//text()"))
            credit_code = "".join(company.xpath(".//p[@class='gggscpnametext']/span[@class='tongyi']//text()"))
            data_label = "".join(company.xpath("./@data-label"))
            name = re.sub(r"\s+", "", name)
            credit_code = re.sub(r"\s+", "", credit_code).replace("统一社会信用代码：", "").replace("注册号：", "")
            data_label = re.sub(r"\s+", "", data_label)
            if search_word in (name, credit_code):
                return data_label
        return 500

    def search_company_detail(self, session_init, data_label):
        detail_url = "https://gsxt.hlj.gov.cn/business/JCXX.jspx"
        params = {
            "id": f"{data_label}",
            "date": f"{int(time.time() * 1000)}"
        }
        response = self.session_retry(session_init, type_="GET", url=detail_url, headers=self.HEADERS, params=params)
        return response.text

    def search_company_detail2(self, session_init, data_label):
        detail_url = f"https://gsxt.hlj.gov.cn/company/detail.jspx"
        params = {
            "id": f"{data_label}",
            "jyzk": "jyzc"
        }
        response = self.session_retry(session_init, type_="GET", url=detail_url, headers=self.HEADERS, params=params)
        return response.text

    def gt_challenge(self, session_init: requests.session):
        gt_url = "https://gsxt.hlj.gov.cn/registerValidate.jspx"
        params = {
            "t": f"{int(time.time() * 1000)}"
        }
        response = self.session_retry(init=session_init, type_="GET", url=gt_url, headers=self.HEADERS, params=params)
        if str(response.json().get("success")) == "0":
            logger.warning(f"存在极验三代离线验证, {response.json}")
        return response.json()

    @staticmethod
    def prd_validate(search_keyword, gt, challenge):
        url = f"http://10.99.206.19:8889/getValidate?challenge={challenge}&gt={gt}&prov=hlj"
        for _ in range(10):
            try:
                response = requests.get(url, timeout=5)
                response = response.json()
                if response.get("status") == 1:
                    return response
                continue
            except:
                logger.warning(f"[{search_keyword}]: 极验3代服务 5秒超时")
                continue
        return {}

    def verify_validate(self, session_init, search_word, challenge, validate):
        search_url = "https://gsxt.hlj.gov.cn/validateSecond.jspx"
        data = {
            "searchText": search_word,
            "geetest_challenge": challenge,
            "geetest_validate": validate,
            "geetest_seccode": f"{validate}|jordan"
        }
        response = self.session_retry(init=session_init, type_="POST", url=search_url, headers=self.HEADERS, data=data)
        return response.json()

    def session_retry(self, init, type_, url, headers, data=None, params=None, timeout=3):
        max_retries = 3
        for retry_count in range(max_retries):
            try:
                # 公共请求参数
                request_kwargs = {
                    'url': url,
                    'headers': headers,
                    'proxies': self.PROXIES,
                    'data': data,
                    'params': params,
                    'timeout': timeout,
                    'verify': False
                }
                # 根据请求类型发送请求
                if type_.upper() == "GET":
                    response = init.get(**request_kwargs)
                elif type_.upper() == "POST":
                    response = init.post(**request_kwargs)
                else:
                    raise ValueError(f"不支持请求类型(要求大写): {type_}")

                # 检查响应状态码
                if response.status_code == 200:
                    return response
                else:
                    logger.warning(
                        f"请求失败 CODE:{response.status_code}. 重试 ({retry_count + 1}/{max_retries}). URL: {url}, 响应文本: {response.text}")
                    del init.cookies["proxyBase"]
                    time.sleep(0.5)

            except Exception as e:
                logger.warning(f"请求错误: {e}. 重试 ({retry_count + 1}/{max_retries}). URL: {url}")
                del init.cookies["proxyBase"]
                time.sleep(0.5)
                continue

        raise Exception("超出单个请求重试次数")