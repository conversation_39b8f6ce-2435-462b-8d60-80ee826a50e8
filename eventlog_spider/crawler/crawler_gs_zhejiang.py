from bs4 import BeautifulSoup, Tag
import re
import json
from urllib.parse import unquote
import requests

from resx.redis_types import RedisQueue, Redis
from resx.config import *
from eventlog_spider.common.eventlog import EventlogOld as Eventlog, SpiderCode
from eventlog_spider.crawler.crawler import Crawler, CrawlerTask, MyException, CrawlerTools
from crawler_log.log_init_v2 import getLogger
from eventlog_spider.parser.parser import ParseTools

logger = getLogger(__name__)


class ZJCrawlerTask(CrawlerTask):
    pass


class ZJCrawler(Crawler, CrawlerTools):

    @classmethod
    def get_name(cls):
        return 'zj'

    def __init__(self, **kwargs):
        self.headers = {
            'Conservation': 'true'
        }
        self.url = 'https://gswsdj.zjzwfw.gov.cn/daprint.do?method=goJbxx&uniscid='
        super().__init__(input_queue=RedisQueue(name='octopus_output_company_tj', **CFG_REDIS_GS, db=5), task_cls=ZJCrawlerTask, eventlog_class=Eventlog,
                         **kwargs)

    def do_crawl(self):
        task: ZJCrawlerTask = self.get_crawler_task()
        eventlog: Eventlog = task.eventlog
        keyword = eventlog.selector.get_info('keyword')
        proxies = {'http': 'http://10.99.192.206:8043', 'https': 'http://10.99.192.206:8043'}

        resp = requests.get(self.url + keyword, headers=self.headers, proxies=proxies, verify=False)
        html = unquote(resp.text)
        soup = BeautifulSoup(html, 'lxml')
        tables = soup.find_all('table')

        company_info = self.parse(tables[0])
        investor_info = self.parse2(tables[1])
        staff_info = []
        change_info = []
        if len(tables) >= 3:
            staff_info = self.parse2(tables[2])
        if len(tables) == 4:
            change_info = self.parse2(tables[3])

        task.pages = {
            'company_info.txt': json.dumps(company_info, ensure_ascii=False),
            'investor_info.txt': json.dumps(investor_info, ensure_ascii=False),
            'staff_info.txt': json.dumps(staff_info, ensure_ascii=False),
            'change_info.txt': json.dumps(change_info, ensure_ascii=False)
        }

    @staticmethod
    def parse2(table: Tag):
        rows = table.find_all('tr')
        info_list = []
        keys = list(rows[0].stripped_strings)
        for row in rows[1:]:
            list_ = list(row.stripped_strings)
            info_list.append(dict(zip(keys, list_)))
        logger.info(info_list)
        return info_list

    @staticmethod
    def parse(table: Tag):
        rows = table.find_all('tr')
        keys, values = [], []
        for row in rows:
            list_ = list(row.stripped_strings)
            keys.extend(list_[::2])
            values.extend(list_[1::2])
        info = dict(zip(keys, values))
        logger.info(info)
        return info
