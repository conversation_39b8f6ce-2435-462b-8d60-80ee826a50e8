import re
from resx.func import to_date
from biz_utils.msv_write import *
from eventlog_spider.common.eventlog import SpiderC<PERSON>, EventlogOld as Eventlog
from eventlog_spider.parser.parser_gs import GSParserTask, GSParser
from resx.log import setup_logger

logger = setup_logger(name=__name__)


class TJParser(GSParser):
    @classmethod
    def get_name(cls):
        return 'tj'

    def __init__(self):
        super().__init__(GSParserTask, msv_source=MSVSource.TJ)

    def do_parse(self):
        task: GSParserTask = self.get_parser_task()
        eventlog: Eventlog = task.eventlog
        pages = task.pages
        keyword = eventlog.selector.get_info('keyword') or eventlog.selector.word

        company_info = pages.get('company_info.txt', None)
        if not company_info:
            logger.warning(f'no page base {eventlog}')
            eventlog.spider_code = SpiderCode.FAIL
            return

        company_info = json.loads(company_info)
        msv_base_info_data = dict(
            name=company_info['entname'],
            # legal_person_name=company_info['lerep'],
            legal_person_name=re.sub(r'([(（]委派代表.*?[）)])$', '', company_info['lerep']),
            reg_number=company_info.get('regno', None),
            company_org_type=company_info.get('enttype', '').strip(),
            reg_location=company_info.get('dom', None),
            estiblish_time=to_date(company_info['estdate']),
            approved_time=to_date(company_info['apprdate']),
            from_time=to_date(company_info.get('opfrom', None)),
            to_time=to_date(company_info.get('opto', None)),
            business_scope=company_info.get('opscope', ''),
            reg_institute=company_info.get('regorg', None),
            reg_status=company_info.get('regstate', None),
            reg_capital=str(company_info['regcap']) + "万元" if company_info.get('regcap') else None,
            credit_code=company_info.get('uniscid', None),
        )
        msv_base_info = MSVBaseInfo.from_dict(msv_base_info_data)
        logger.info(f'{keyword} 基本信息解析完成')

        msv_partnerships = None
        company_org_type = msv_base_info.company_org_type
        legal_person_name = msv_base_info.legal_person_name
        if company_org_type and '合伙' in company_org_type and legal_person_name:
            msv_partnerships = [MSVPartnership.from_dict(dict(executive=x)) for x in legal_person_name.split(';')]
        task.set_base(msv_base_info, msv_partnerships)

        msv_investors = [MSVInvestor.from_dict(
            {
                'investor_name': x['inv'],
                'investor_type': 0,
                # 'capital': x['subscription'] if x['详情'] != '-' else [],
                # 'capital_actl': x['paid_in'] if x['详情'] != '-' else [],
                'detail': {
                    '证照类型': x.get('blictype', ''),
                    '证照编号': x.get('blicno', ''),
                    'investor_type': x.get('invtype', '')
                }
            }) for x in json.loads(pages['investor_info.txt'])]
        task.set_investors(msv_investors)
        logger.info(f'{keyword} 股东信息解析完成')

        msv_staff = [MSVStaff.from_dict({
            'staff_name': i['NAME'],
            'staff_position': i['POSITION']
        }) for i in json.loads(pages['staff_info.txt']) if i != {}]
        task.set_staffs(msv_staff)
        logger.info(f'{keyword} 主要人员解析完成')

        msv_change = [MSVChange.from_dict(
            {
                'change_item': row['altitem'],
                'change_time': to_date(row['altdate']),
                'content_before': row.get('altbe', ''),
                'content_after': row.get('altaf', '')
            }
        ) for row in json.loads(pages['change_info.txt'])]
        task.set_changes(msv_change)
        logger.info(f'{keyword} 变更信息解析完成')
