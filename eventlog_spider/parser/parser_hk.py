import json
from datetime import datetime, date, timedelta
from pydantic import Field, conint
from typing import Optional, List, Union
import re
from zhconv import convert

from resx.base_model import BaseModel
from eventlog_spider.common.eventlog import EventlogNew as Eventlog, SpiderCode
from eventlog_spider.parser.parser import Pa<PERSON>rTask, Parser, ParseTools
from resx.config import *
from resx.log import setup_logger
from resx.ext.mysql_rw_splitting import MySQLRWSplittingDao
from biz_utils.msv_write import MSVSource
from biz_utils.msv_write import msv_write_hk_company_base_info

logger = setup_logger(name=__name__)


class ParserHKTask(ParserTask):
    def __init__(self, eventlog: Eventlog, pages):
        super().__init__(eventlog, pages)
        self.log_param_ctx = {'company': eventlog.selector.word, 'keyword': eventlog.selector.word}


class ParserHK(Parser, ParseTools):
    @classmethod
    def get_name(cls):
        return 'hk'

    def __init__(self):
        self.source_url = 'https://www.e-services.cr.gov.hk/ICRIS3ES/ps/company-name/base-information/search-brno.do'
        self.origin_url = 'https://jindi-oss-companyinfo.oss-cn-beijing.aliyuncs.com/channel/'
        self.CoyType = {
            ("I", "B"): '私人股份有限公司',
            ("I", "C"): '担保有限公司',
            ("I", "A"): '公众股份有限公司',
            ("U", "E"): '私人无限公司',
            ("U", "D"): '公众无限公司',
            ("F", None): '注册非香港公司',
            ("FM", None): '曾登记押记的非注册海外法团',
            ("LF", None): '有限合伙基金',
            ('LP', None): '有限责任合伙',
            ('MF', None): '互惠基金',
            ('OF', None): '开放式基金型公司',
            ('RT', None): '注册受托人法团',
            ('SO', None): '根据特别条例成立的法人团体',
            ('TC', None): '登记的信托公司',
            ('UC', None): '非注册公司',
        }
        self.actStatus = {
            'L': ('仍注册', '仍註冊'),
            'C': ('已终止营业地点', '已終止營業地點'),
            'D': ('已告解散', '已告解散'),
            'A': ('合并后不再是独立的实体', '合併後不再是獨立的實體'),
        }
        self.dao_hk = MySQLRWSplittingDao(
            mysql_ro=CFG_MYSQL_GS_OUTER,
            db_tb_name='prism.company_hk',
            primary_index_fields=(['br_num'], []),
            entity_class=CompanyHk,
            dim='香港公司基本信息',
            ignore_fields=['id', 'company_id', 'updatetime']
        )
        # self.dao_bp = MySQLRWSplittingDao(
        #     mysql_ro=CFG_MYSQL_ZX_RDS110,
        #     db_tb_name='data_listed_company.company_bond_plates',
        #     dim='债券板块信息'
        # )
        self.dao_address = MySQLRWSplittingDao(
            mysql_ro=CFG_MYSQL_GS_OUTER,
            db_tb_name='prism.hk_registered_office_new',
            primary_index_fields=(['hk_id'], []),
            entity_class=HkRegisteredOfficeNew,
            dim='香港地址',
            ignore_fields=['id', 'create_time']
        )
        self.dao_name_records = MySQLRWSplittingDao(
            mysql_ro=CFG_MYSQL_GS_OUTER,
            db_tb_name='prism.hk_company_name_records',
            primary_index_fields=(['hk_id'], ['change_time']),
            entity_class=HkCompanyNameRecords,
            dim='香港曾用名',
            ignore_fields=['id', 'create_time']
        )
        self.dao_image = MySQLRWSplittingDao(
            mysql_ro=CFG_MYSQL_GS_OUTER,
            db_tb_name='prism.hk_image_list',
            primary_index_fields=(['hk_id'], ['image_id_str']),
            entity_class=HkImageList,
            dim='香港官方文档',
            ignore_fields=['id', 'create_time']
        )
        super().__init__(task_cls=ParserHKTask)

    def do_parse(self, *args, **kwargs):
        task: ParserHKTask = self.get_parser_task()
        eventlog = task.eventlog
        base_info: Eventlog = json.loads(task.pages['res.json'])
        brno = base_info['brno']
        address1 = json.loads(task.pages['address1.json'])
        address2 = json.loads(task.pages['address2.json'])
        res_his = json.loads(task.pages['res_his.json'])
        doc_list = json.loads(task.pages['doc_list.json'])
        image_types = json.loads(task.pages['image_types.json'])
        try:
            # company_hk / hk_registered_office_new
            hk_id, change_fields = self.base_info_parser(task, base_info, brno, address1, address2)
            # 曾用名 hk_company_name_records
            self.prev_info_parser(task, res_his, hk_id, brno)
            # image
            self.parser_image(task, doc_list, image_types, hk_id)

            eventlog.spider.ab_info = change_fields
            logger.info(f'解析完成 brno: {brno} ab_info: {eventlog.spider.ab_info}')
        except Exception as e:
            error = self.custom_traceback(e)
            logger.error(f"brno: [{brno}] 解析错误 \n {error}")
            eventlog.code = SpiderCode.FAIL

    def parser_image(self, task: ParserHKTask, image_list, image_types, hk_id):
        list_ = []
        for image in image_list:
            info = {
                'hk_id': hk_id,
                'image_id_str': image['docID'],
                'image_id': 0,
                'image_name': image['docChNameDisplay'],
                'image_name_s': convert(image['docChNameDisplay'], "zh-hans"),
                'filing_date': datetime.strptime(image['filingDate'], "%d-%b-%Y %H:%M") if image.get('filingDate') and image.get('filingDate') != '-' else None,
                'pages': image.get('noOfPg'),
                'size': image.get('fileSize'),
                'state': '可供查阅' if 'AVAILABLE' in image['statusForDisplay'] else '不可查阅',
                'image_type': image_types[image['docID']]
            }
            list_.append(HkImageList(**info))
        if not list_:
            return
        self.dao_image.save_group(list_, [hk_id], task.log_param_ctx)

    def base_info_parser(self, task: ParserHKTask, base_info, brno, address1, address2):
        """
            如果公司没有中文名:
            1. 首先去rds101库data_listed_company.company_bond_plates表查找有没有中文名称
        """
        analytic_info = dict()
        F = False
        if base_info.get('corpCoyName', ''):
            # todo 香港上市境外公司
            corpCoyName = self.clean(base_info.get('corpCoyName'))
            otherCoyName = self.clean(base_info.get('otherCoyName'))
            if corpCoyName:
                en_name, cn_name = (corpCoyName, otherCoyName) if re.fullmatch(r'[0-9A-Za-z\s（）().,]+', corpCoyName) else (otherCoyName, corpCoyName)
            else:
                en_name, cn_name = (otherCoyName, corpCoyName) if re.fullmatch(r'[0-9A-Za-z\s（）().,]+', otherCoyName) else (corpCoyName, otherCoyName)
            analytic_info.update({
                'property1': base_info.get('otherCoyName'),
                'property2': self.clean(base_info.get('ctryScName') if base_info.get('ctryScName') else base_info.get('ctryTcName')),
                'property3': str(self.timestamp2datetime(base_info.get('cpbDate')))
            })
        else:
            # todo 香港上市境内公司
            F = True
            en_name, cn_name = self.clean(base_info.get('engCoyName')), self.clean(base_info.get('chiCoyName'))
            cn_name = self.replace_bracket(cn_name)

        remarks = ''
        if base_info['coyTyp'] in ['I', 'U', 'F'] and base_info['coyStatus'] == "L":
            remarks = '上市公司'

        analytic_info.update({
            'br_num': base_info['brno'],
            'company_num': base_info['company_num'] if base_info['company_num'] != 'PS.COMMON.LBL.NOT_APPLICABLE' else '',
            'name': f'{en_name} {cn_name}'.strip(),
            'name_s': f'{en_name} {convert(cn_name, "zh-hans")}'.strip(),
            'name_cn': cn_name,
            'name_cn_s': convert(cn_name, "zh-hans"),
            'name_en': en_name,
            'company_org_type': self.CoyType[(base_info['coyTyp'], base_info['coyCat'])],
            'company_org_type_s': convert(self.CoyType[(base_info['coyTyp'], base_info['coyCat'])], "zh-hans"),
            'estiblish_time': self.timestamp2datetime(base_info['incorpPlcEffDate'] if base_info.get('incorpPlcEffDate') else base_info.get('incorpRegDate')),
            'reg_status': self.actStatus[base_info['actStatus']][1],
            'reg_status_s': self.actStatus[base_info['actStatus']][0],
            'remarks': remarks if remarks else base_info['tcRemarks'],
            'remarks_s': base_info['scRemarks'],
            'liquidation_mode': self.extract_text_in_brackets(base_info['tcRemarks']),
            'liquidation_mode_s': self.extract_text_in_brackets(base_info['scRemarks']),
            'to_time': self.timestamp2datetime(base_info.get('dissDate')),
            'mortgage': '有' if base_info.get('chrgInd') else '無',
            'mortgage_s': '有' if base_info.get('chrgInd') else '无',
            'important_items': convert(base_info.get('note') if base_info.get('note') else '', "zh-hk"),
            'important_items_s': convert(base_info.get('note') if base_info.get('note') else '', "zh-hans"),
            'source': f'{self.source_url}?brNo={base_info.get("brno", "")}'
        })
        self.datatime_to_str(analytic_info)
        ret = msv_write_hk_company_base_info(br_num=brno, source=MSVSource.HK, item=analytic_info)
        logger.info(ret)
        company_hk: Optional[CompanyHk] = self.dao_hk.get(br_num=brno)
        _, change_fields = self.compare_print(analytic_info, company_hk.model_dump() if company_hk else {})
        self.dao_hk.save(CompanyHk(**analytic_info), task.log_param_ctx)
        logger.info(f'brno: {brno} 基本信息: {analytic_info} ab_info: {change_fields}')

        # if not cn_name and company_hk.company_id:
        #     p = self.dao_bp.get(company_id=company_hk.company_id, type='港股')
        #     if p and self.majority_letters(p['company_name']):
        #         cn_name = p['company_name']
        #         company_hk.name = f'{en_name} {cn_name}'
        #         company_hk.name_s = f'{en_name} {convert(cn_name, "zh-hans")}'
        #         company_hk.name_cn = cn_name
        #         company_hk.name_cn_s = convert(cn_name, "zh-hans")
        #         self.dao_hk.save(company_hk)

        info = {
            'hk_id': company_hk.id,
            'registered_office': base_info.get('displayAddr') if base_info.get('displayAddr') else "",
            'office_type': '主要营业地点' if F else '注册办事处'
        }
        address_pre: HkRegisteredOfficeNew = self.dao_address.get(hk_id=company_hk.id)
        address = address_pre.registered_office if address_pre else ""
        if not address and not info.get('registered_office'):
            address2 = address2 if not address1 else address1
            if address2:
                info['registered_office'] = address2[0]['Address_of_Registered_Office']
                self.dao_address.save(HkRegisteredOfficeNew(**info), task.log_param_ctx)
                logger.info('brno: [{}] 地址 - {}'.format(brno, str(info)))

        return company_hk.id, change_fields

    def prev_info_parser(self, task, base_prev, hk_id, brno):
        try:
            name_record_list: List[HkCompanyNameRecords] = []
            if not base_prev:
                logger.error('brno: [{}] - 曾用名内容为空'.format(brno))
                return
            for base in base_prev:
                info = dict()
                info['hk_id'] = hk_id
                if len(base['dplyNameList']) == 2:
                    info['history_name_cn'] = base['dplyNameList'][1]
                    info['history_name_en'] = base['dplyNameList'][0]
                elif len(base['dplyNameList']) == 1:
                    if self.majority_letters(base['dplyNameList'][0]):
                        info['history_name_cn'] = base['dplyNameList'][0]
                    else:
                        info['history_name_en'] = base['dplyNameList'][0]
                info['change_time'] = self.timestamp2datetime(base['startEffDate'])
                info['update_time'] = datetime.now()
                info['history_name_cn'] = self.clean(info['history_name_cn']) if 'history_name_cn' in info else ''
                name_record = HkCompanyNameRecords.from_dict(info)
                if name_record:
                    logger.info('brno: [{}] 曾用名 - {}'.format(brno, info))
                    name_record_list.append(name_record)
            self.dao_name_records.save_group(name_record_list, [hk_id], task.log_param_ctx)
        except Exception as e:
            logger.error('hk_company_name_records表存储出现问题')
            self.custom_traceback(e)

    @staticmethod
    def clean(s):
        if not s:
            return ''
        s = s.strip()
        s = re.sub(r'[\n\r\t：∶]', '', s)
        s = re.sub(r'\s{2,}', ' ', s)
        return s

    @staticmethod
    def timestamp2datetime(timestamp):
        if timestamp == '-' or not timestamp:
            return None
        timestamp = int(timestamp)
        if timestamp < 0:
            epoch = datetime(1970, 1, 1)
            return epoch + timedelta(seconds=timestamp // 1000) + timedelta(hours=8)
        return datetime.fromtimestamp(timestamp // 1000)

    @staticmethod
    def majority_letters(text):
        return re.fullmatch(r'^[\u4e00-\u9fff0-9（）()\s]+$', text)

    @staticmethod
    def extract_text_in_brackets(text):
        matches = re.findall(r'\((.*?)\)', text)
        if not matches:
            return '-'
        return matches[0]


class CompanyHk(BaseModel):
    id: conint(strict=True, ge=0) = Field(default=0)
    company_id: Optional[int] = Field(default=None)
    br_num: str = Field(default='')
    company_num: Optional[str] = Field(default='')
    base: Optional[str] = Field(default='hk')
    name: Optional[str] = Field(default='')
    name_s: Optional[str] = Field(default='')
    name_cn: Optional[str] = Field(default='')
    name_cn_s: Optional[str] = Field(default='')
    name_en: Optional[str] = Field(default='')
    company_org_type: Optional[str] = Field(default='')
    company_org_type_s: Optional[str] = Field(default='')
    estiblish_time: Optional[datetime] = Field(default=None)
    reg_status: Optional[str] = Field(default='')
    reg_status_s: Optional[str] = Field(default='')
    remarks: Optional[str] = Field(default='')
    remarks_s: Optional[str] = Field(default='')
    liquidation_mode: Optional[str] = Field(default='')
    liquidation_mode_s: Optional[str] = Field(default='')
    to_time: Optional[datetime] = Field(default=None)
    mortgage: Optional[str] = Field(default='')
    mortgage_s: Optional[str] = Field(default='')
    important_items: Optional[str] = Field(default='')
    important_items_s: Optional[str] = Field(default='')
    source: Optional[str] = Field(default='')
    crawledtime: Optional[datetime] = Field(default_factory=datetime.now)
    updatetime: Optional[datetime] = Field(default_factory=datetime.now)
    deleted: int = Field(default=0)
    property1: Optional[str] = Field(default='')
    property2: Optional[str] = Field(default='')
    property3: Optional[str] = Field(default='')
    exist_report: Optional[int] = Field(default=0)


class HkRegisteredOfficeNew(BaseModel):
    id: conint(strict=True, ge=0) = Field(default=0)
    hk_id: int = Field(default=0)
    registered_office: Optional[str] = Field(default='')
    office_type: Optional[str] = Field(default='')
    create_time: Optional[datetime] = Field(default_factory=datetime.now)
    update_time: Optional[datetime] = Field(default_factory=datetime.now)


class HkCompanyNameRecords(BaseModel):
    id: conint(strict=True, ge=0) = Field(default=0)
    hk_id: int
    history_name_cn: str = Field(default='')
    history_name_en: str = Field(default='')
    change_time: datetime
    create_time: datetime = Field(default_factory=datetime.now)
    update_time: datetime = Field(default_factory=datetime.now)


class HkImageList(BaseModel):
    id: conint(strict=True, ge=0) = Field(default=0)
    hk_id: int = Field(default=0)
    image_id_str: str = Field(default='')
    image_id: int = Field(default=0)
    image_name: Optional[str] = Field(default='')
    image_name_s: Optional[str] = Field(default='')
    filing_date: Union[datetime, str] = Field(default=None)
    pages: Optional[int] = Field(default=0)
    size: Optional[str] = Field(default='')
    state: Optional[str] = Field(default='')
    image_type: Optional[str] = Field(default='')
    create_time: Optional[datetime] = Field(default_factory=datetime.now)
    update_time: Optional[datetime] = Field(default_factory=datetime.now)
