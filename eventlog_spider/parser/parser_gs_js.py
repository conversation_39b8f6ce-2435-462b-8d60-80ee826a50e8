# encoding=utf8
import datetime
import re
from resx.func import to_date, to_datetime
from biz_utils.msv_write import *
from eventlog_spider.common.eventlog import EventlogOld as Eventlog
from eventlog_spider.parser.parser_gs import <PERSON><PERSON>arserTask, GSParser
from resx.log import setup_logger
from biz_utils.entity.gs_others.company_abnormal_info import CompanyAbnormalInfoDao

logger = setup_logger(name=__name__)


class GSJSParserTask(GSParserTask):
    def __init__(self, eventlog: Eventlog, pages):
        self.search = eventlog.selector.get_info('keyword')
        self.company_id = eventlog.selector.word
        super().__init__(eventlog, pages)


class GSJSParser(GSParser):
    def __init__(self):
        self.abnormal_dao = CompanyAbnormalInfoDao()
        super().__init__(task_cls=GSJSParserTask, msv_source=MSVSource.JS)

    @classmethod
    def get_name(cls):
        return 'js'

    def do_parse(self):
        """解析企业信息，包括基本信息、注册号、股东、主要人员和变更记录，并将结果写入多源多版本记录。"""
        task = self.get_parser_task()
        company_id = task.company_id
        logger.info(f"[{task.search}]: 开始解析...")

        # 初始化结果dict
        final_result = dict()

        # 照面信息解析
        basic_result = self.parser_basic_info(task.pages.get("detail_info.txt"))
        final_result["basic_info"] = basic_result
        logger.info(f"[{task.search}]: 照面信息解析完成 - {basic_result}")

        # 注册号解析
        reg_num = self.parser_search_list(task.pages.get("search_list.txt"))
        final_result["basic_info"]["reg_num"] = reg_num
        logger.info(f"[{task.search}]: 注册号解析完成 - {reg_num}")

        # 股东解析
        investor_list, investor_info_list = [], []
        investor_key_list = [key for key in task.pages if key.startswith("investor_list")]
        investor_info_key_list = [key for key in task.pages if key.startswith("investor_info_list")]
        for investor_group in investor_key_list:
            investor_list.extend(json.loads(task.pages[investor_group]).get("data", ""))
        for investor_info_group in investor_info_key_list:
            investor_info_list.extend(json.loads(task.pages[investor_info_group]))
        investor_result = self.parser_investor_info(investor_list, investor_info_list)
        final_result["investor_info"] = investor_result
        logger.info(f"[{task.search}]: 股东信息解析完成 - {investor_result}")

        # 主要人员信息解析
        staff_result = self.parser_staff_info(task.pages.get("staff_list.txt", ""))
        final_result["staff_info"] = staff_result
        logger.info(f"[{task.search}]: 主要人员信息解析完成 - {staff_result}")

        # 变更记录信息解析
        change_list = []
        change_key_list = [key for key in task.pages if key.startswith("change_list")]
        for change_group in change_key_list:
            change_list.extend(json.loads(task.pages[change_group]).get("data", ""))
        change_result = self.parser_change_info(change_list)
        final_result["change_info"] = change_result
        logger.info(f"[{task.search}]: 变更记录信息解析完成 - {change_result}")

        # 经营异常
        abnormal_list = [key for key in task.pages if key.startswith("abnormal_list")]
        for abnormal_ in abnormal_list:
            final_result[f"{abnormal_.rstrip('.json')}"] = self.parser_abnormal_info(task.pages.get(f"{abnormal_}"))
        logger.info(f"[{task.search}]: 经营异常信息解析完成")

        # 照面信息写入多源多版本记录
        msv_basic_info = self.write_msv_basic_info(final_result["basic_info"])
        company_type, company_legal_name = msv_basic_info.company_org_type, msv_basic_info.legal_person_name
        # 判断是不是合伙企业 且 不是分支机构
        msv_partnerships = []
        if company_type and company_legal_name and "合伙" in company_type and "分支机构" not in company_type:
            # 处理合伙企业的法人代表
            legal_names = company_legal_name.split("、") if "、" in company_legal_name else [company_legal_name]
            for person_name in legal_names:
                executive = re.sub(r"[(（]委派代表.*[）)]", "", person_name)
                represen = ""
                if "委派代表" in person_name:
                    represen = re.findall(r"委派代表[:：](.*)[）)]", person_name)
                    represen = represen[0].replace("委派代表:", "").replace("委派代表：", "") if represen else ""
                msv_partnerships.append(MSVPartnership.from_dict(dict(executive=executive, represen=represen)))
            msv_basic_info.legal_person_name = None
        task.set_base(msv_basic_info, msv_partnerships)

        # 股东信息写入多源多版本记录
        # 农合社 93开头 没有股东 就用主要人员充当股东 写入 股东表
        if final_result["basic_info"].get("credit_code").startswith("93") and not final_result["investor_info"]:
            # 构造股东
            msv_investor_result = self.prd_msv_investor_from_staff(final_result["staff_info"])
        else:
            msv_investor_result = self.write_msv_investor_info(final_result["investor_info"])
        task.set_investors(msv_investor_result)

        # 主要人员写入多源多版本记录
        msv_staff_result = self.write_msv_staff_info(final_result["staff_info"])
        task.set_staffs(msv_staff_result)

        # 变更记录写入多源多版本记录
        msv_change_result = self.write_msv_change_info(final_result["change_info"])
        task.set_changes(msv_change_result)

        # 经营异常
        abnormal_list = self.conformity_abnormal_list(final_result)
        if abnormal_list:
            msv_abnormal_result = self.write_msv_abnormal_info(abnormal_list)
            task.set_abnormals(msv_abnormal_result)
            # self.rem_abnormal_for_mysql(company_id, abnormal_list)

            logger.info(f"[{task.event_id}] 经营异常写入完成")

        logger.info(f"[{task.search}]: 解析记录完成。")

    @staticmethod
    def conformity_abnormal_list(data):
        abnormal_list = [key for key in data if key.startswith("abnormal_list")]
        abnormals_list = []
        for abnormal_num in abnormal_list:
            for abnormal_ in data[abnormal_num].values():
                abnormals_list.append(abnormal_)
        return abnormals_list

    # def rem_abnormal_for_mysql(self, company_id, abnormal_info):
    #     abnormal_list_from_mysql = list(self.abnormal_dao.get_many(company_id=company_id))
    #     for abnormal_item in abnormal_list_from_mysql:
    #         abnormal_json = abnormal_item.to_dict()
    #
    #         id = abnormal_json.get("id")
    #         put_reason = abnormal_json.get("put_reason")
    #         put_date = abnormal_json.get("put_date")
    #         put_department = abnormal_json.get("put_department")
    #         deleted = abnormal_json.get("deleted")
    #
    #         for new_abnormal_item in abnormal_info:
    #             if isinstance(new_abnormal_item["put_date"], datetime.datetime):
    #                 new_abnormal_item["put_date"] = new_abnormal_item["put_date"].strftime("%Y-%m-%d")
    #             new_abnormal_item["put_date"] = new_abnormal_item.get("put_date").replace("T", " ") if "T" in new_abnormal_item.get("put_date") else new_abnormal_item.get("put_date")
    #             # 匹配到了 deleted = 0 的 经营异常
    #             new_abnormal_item["put_date"] = to_datetime(new_abnormal_item.get("put_date"))
    #             put_date = to_datetime(put_date)
    #             if (new_abnormal_item.get("put_reason").startswith(put_reason) or put_reason.startswith(new_abnormal_item.get("put_reason"))) \
    #                 and (new_abnormal_item.get("put_department").startswith(put_department) or put_department.startswith(new_abnormal_item.get("put_department"))) \
    #                 and new_abnormal_item.get("put_date") == put_date and deleted in (0, "0"):
    #                 # 如果存在 任意一个 remove 项 就进行更新
    #                 if new_abnormal_item.get("rem_reason") or new_abnormal_item.get("rem_date") or new_abnormal_item.get("rem_department"):
    #                     # 更新 rem_reason rem_date rem_department
    #                     item = self.abnormal_dao.get(id=id)
    #                     item.deleted = 1
    #                     item.remove_reason = new_abnormal_item.get("rem_reason")
    #                     item.remove_date = to_datetime(new_abnormal_item.get("rem_date"))
    #                     item.remove_department = new_abnormal_item.get("rem_department")
    #                     self.abnormal_dao.save(item)
    #                     logger.info(f"江苏工商-经营异常判定: company_id {company_id} 经营异常信息ID {id}, 移入历史。")

    def parser_abnormal_info(self, text):
        if text:
            final_abnormal = dict()
            abnormal_info = json.loads(text).get("data", {})
            for index, abnormal_ in enumerate(abnormal_info):
                # 列入原因
                put_reason = abnormal_["FACT_REASON"]
                # 列入时间
                put_date = abnormal_["MARK_DATE"]
                # 列入机关
                put_department = abnormal_["NEW_DESIDE_ORG"]
                # 移除原因
                rem_reason = abnormal_["REMOVE_REASON"]
                # 移除时间
                rem_date = abnormal_["REMOVE_DATE"]
                # 移除机关
                rem_department = abnormal_["REMOVE_DESIDE_ORGNAME"]
                # 列入原因分类
                put_reason_type = ""
                # 移除原因分类
                rem_reason_type = ""

                final_abnormal[f"abnormal_{index+1}"] = dict()
                final_abnormal[f"abnormal_{index+1}"]["put_reason"] = put_reason
                final_abnormal[f"abnormal_{index+1}"]["put_date"] = put_date
                final_abnormal[f"abnormal_{index+1}"]["put_department"] = put_department
                final_abnormal[f"abnormal_{index+1}"]["rem_reason"] = rem_reason
                final_abnormal[f"abnormal_{index+1}"]["rem_date"] = rem_date
                final_abnormal[f"abnormal_{index+1}"]["rem_department"] = rem_department
                final_abnormal[f"abnormal_{index+1}"]["put_reason_type"] = put_reason_type
                final_abnormal[f"abnormal_{index+1}"]["rem_reason_type"] = rem_reason_type
            return final_abnormal
        else:
            return {}

    @staticmethod
    def parser_search_list(search_list: str) -> str:
        """解析搜索列表，返回第一个项目的 UNI_SCID。
        Args:
            search_list (str): 搜索列表的 JSON 字符串。
        Returns:
            str: 第一个项目的 UNI_SCID，如果没有则返回空字符串。
        """
        if search_list:
            search_json = json.loads(search_list)
            return search_json["items"][0].get("UNI_SCID", "")
        return ""

    @staticmethod
    def parser_basic_info(homepage_info: str) -> dict:
        """解析主页信息，提取基本企业信息。
        Args:
            homepage_info (str): 企业主页信息的 JSON 字符串。
        Returns:
            dict: 包含企业基本信息的字典。
        """
        homepage_json = json.loads(homepage_info)
        basic_info = dict()
        basic_info["credit_code"] = homepage_json["REG_NO"]  # 统代/注册号
        basic_info["name"] = homepage_json["CORP_NAME"]  # 企业名称
        basic_info["company_org_type"] = homepage_json["ZJ_ECON_KIND"]  # 企业类型
        basic_info["legal_person_name"] = homepage_json["OPER_MAN_NAME"]  # 法人代表 / 执行事务合伙人
        basic_info["reg_capital"] = homepage_json["REG_CAPI_WS"] if homepage_json["ADMIT_MAIN"] == "10" else \
        homepage_json["REG_CAPI"]  # 注册资本 10开头使用REG_CAPI_WS else REG_CAPI
        basic_info["establish_time"] = homepage_json["START_DATE"]  # 成立日期
        basic_info["from_time"] = homepage_json["FARE_TERM_START"]  # 营业期限自
        basic_info["to_time"] = homepage_json["FARE_TERM_END"]  # 营业期限至
        basic_info["reg_institute"] = homepage_json["BELONG_ORG"]  # 登记机关
        basic_info["approved_time"] = homepage_json["CHECK_DATE"]  # 核准日期
        basic_info["reg_location"] = homepage_json["FARE_PLACE"] if homepage_json["ADMIT_MAIN"] == "01" and \
                                                                    homepage_json["FARE_PLACE"] else homepage_json[
            "ADDR"]  # 住所
        basic_info["reg_status"] = homepage_json["CORP_STATUS"]  # 登记状态
        basic_info["off_date"] = homepage_json["WRITEOFF_DATE"]  # 注销日期
        basic_info["off_reason"] = homepage_json["WRITEOFF_REASON"]  # 注销原因
        basic_info["revoke_date"] = homepage_json["REVOKE_DATE"]  # 吊销日期
        basic_info["revoke_reason"] = homepage_json["REVOKE_REASON"]  # 吊销原因
        basic_info["business_scope"] = homepage_json["FARE_SCOPE"]  # 经营范围
        return basic_info

    @staticmethod
    def parser_investor_info(investor_list: list, investor_info_list: list):
        """解析股东信息，结合股东列表和股东详细信息列表。
        Args:
            investor_list (list): 股东列表。
            investor_info_list (list): 股东详细信息列表。
        Returns:
            list: 解析后的股东信息列表。
        """
        investor_list_result = list()
        for investor_ in investor_list:
            for investor_info_ in investor_info_list:
                if investor_["ID"] == investor_info_["investor_id"]:
                    investor_result = dict()
                    investor_result["capital"] = GSJSParser.prd_capital(investor_info_["rj"]["data"], "rj")  # 认缴
                    investor_result["capital_actl"] = GSJSParser.prd_capital(investor_info_["sj"]["data"],
                                                                                "sj")  # 实缴
                    investor_result["investor_id"] = investor_["ID"]  # 股东id
                    investor_result["investor_name"] = investor_["STOCK_NAME"]  # 股东名
                    investor_result["investor_type"] = investor_["STOCK_TYPE"]  # 股东类型
                    investor_result["card_type"] = investor_["IDENT_TYPE_NAME"]  # 执照类型
                    investor_result["card_no"] = investor_["IDENT_NO"]  # 执照号
                    investor_list_result.append(investor_result)
        return investor_list_result

    @staticmethod
    def prd_msv_investor_from_staff(staff_list: list) -> list:
        """从主要人员列表中生成股东信息。
        Args:
            staff_list (list): 主要人员列表。
        Returns:
            list: 生成的股东信息列表。
        """
        investor_set, investor_list = set(), list()
        for staff in staff_list:
            investor = dict()
            investor["capital"] = [{"amomon": "", "time": "", "paymet": ""}]
            investor["capital_actl"] = [{"amomon": "", "time": "", "paymet": ""}]
            investor["investor_name"] = staff["staff_name"]  # 股东名
            investor["investor_type"] = 0
            investor["card_type"] = ""
            investor["card_no"] = ""
            investor["detail"] = {
                'shareholderName': staff["staff_name"]
            }
            investor_set.add(str(investor))  # 用于查看是否有重复股东
            investor_list.append(MSVInvestor.from_dict(investor))  # 收录股东，待写入多源多版本
            if len(investor_set) != len(investor_list):
                investor_list.remove(MSVInvestor.from_dict(investor))  # 移除重复股东
        return investor_list

    @staticmethod
    def prd_capital(capital_list: list, capital_type: str) -> list:
        """处理资本信息，返回格式化后的资本列表。
        Args:
            capital_list (list): 资本信息列表。
            capital_type (str): 资本类型（认缴或实缴）。
        Returns:
            list: 格式化后的资本信息列表。
        """
        capital_result = list()
        capital_ins = dict()
        capital_ins["amomon"] = ""
        capital_ins["time"] = ""
        capital_ins["paymet"] = ""

        if capital_list:
            for capital in capital_list:
                capital_dict = dict()
                if capital_type == "rj":
                    amonon = re.findall(r"([\d.]+)", capital.get("SHOULD_CAPI"))[0]
                    capital_dict["amomon"] = amonon if amonon else ""
                    capital_dict["time"] = capital["SHOULD_CAPI_DATE"] if capital.get("SHOULD_CAPI_DATE") else ""
                    capital_dict["paymet"] = capital["INVEST_TYPE_NAME"] if capital.get("INVEST_TYPE_NAME") else ""
                    capital_result.append(capital_dict)

                elif capital_type == "sj":
                    amonon = re.findall(r"([\d.]+)", capital.get("REAL_CAPI"))[0]
                    capital_dict["amomon"] = amonon if amonon else ""
                    capital_dict["time"] = capital["REAL_CAPI_DATE"] if capital.get("REAL_CAPI_DATE") else ""
                    capital_dict["paymet"] = capital["INVEST_TYPE_NAME"] if capital.get("INVEST_TYPE_NAME") else ""
                    capital_result.append(capital_dict)

                else:
                    capital_dict["amomon"] = ""
                    capital_dict["time"] = ""
                    capital_dict["paymet"] = ""
                    capital_result.append(capital_dict)
        else:
            capital_result.append(capital_ins)

        return capital_result

    @staticmethod
    def parser_staff_info(staff_list: str) -> list:
        """解析主要人员信息。
        Args:
            staff_list (str): 主要人员信息的 JSON 字符串。
        Returns:
            list: 解析后的主要人员信息列表。
        """
        staff_list = json.loads(staff_list)
        staff_result = list()
        for staff_item in staff_list:
            staff_ = dict()
            staff_["staff_name"] = staff_item["PERSON_NAME"] if staff_item.get("PERSON_NAME") else ""
            staff_["staff_position"] = staff_item["POSITION_NAME"] if staff_item.get("POSITION_NAME") else ""
            staff_["staff_other_info"] = ""
            staff_result.append(staff_)
        return staff_result

    @staticmethod
    def parser_change_info(change_list: list) -> list:
        """解析变更记录信息。
        Args:
            change_list (list): 变更记录列表。
        Returns:
            list: 解析后的变更记录信息列表。
        """
        change_result = list()
        for change_item in change_list:
            change_ = dict()
            change_["change_item"] = change_item["CHANGE_ITEM_NAME"] if change_item.get("CHANGE_ITEM_NAME") else ""
            change_["change_time"] = change_item["CHANGE_DATE"] if change_item.get("CHANGE_DATE") else ""
            change_["content_before"] = change_item["OLD_CONTENT"] if change_item.get("OLD_CONTENT") else ""
            change_["content_after"] = change_item["NEW_CONTENT"] if change_item.get("NEW_CONTENT") else ""
            change_result.append(change_)
        return change_result

    @staticmethod
    def write_msv_basic_info(basic_info: dict) -> Optional[MSVBaseInfo]:
        """将基本信息写入多源多版本记录。
        Args:
            basic_info (dict): 企业基本信息字典。
        Returns:
            MSVBaseInfo: 写入后的基本信息对象。
        """
        if basic_info:
            msv_base_info_data = dict(
                name=basic_info["name"] if basic_info.get("name") else "",
                legal_person_name=basic_info["legal_person_name"] if basic_info.get("legal_person_name") else "",
                company_org_type=basic_info["company_org_type"] if basic_info.get("company_org_type") else "",
                reg_location=basic_info["reg_location"] if basic_info.get("reg_location") else "",
                estiblish_time=to_date(basic_info["establish_time"]) if basic_info.get("establish_time") else None,
                from_time=to_date(basic_info["from_time"]) if basic_info.get("from_time") else None,
                to_time=to_date(basic_info["to_time"]) if basic_info.get("to_time") else None,
                business_scope=basic_info["business_scope"] if basic_info.get("business_scope") else "",
                reg_status=basic_info["reg_status"] if basic_info.get("reg_status") else "",
                reg_capital=basic_info["reg_capital"] if basic_info.get("reg_capital") else "",
                credit_code=basic_info["credit_code"] if basic_info.get("credit_code") else "",
                reg_number=basic_info["reg_num"] if basic_info.get("reg_num") else "",
                approved_time=to_date(basic_info["approved_time"]) if basic_info.get("approved_time") else None,
                reg_institute=basic_info["reg_institute"] if basic_info.get("reg_institute") else ""
            )
            msv_base_info = MSVBaseInfo.from_dict(msv_base_info_data)
            return msv_base_info
        return None

    @staticmethod
    def write_msv_investor_info(investor_info: list) -> list:
        """将股东信息写入多源多版本记录。
        Args:
            investor_info (list): 股东信息列表。
        Returns:
            list: 写入后的股东信息列表。
        """
        msv_investor_set, msv_investor_list = set(), list()
        for investor in investor_info:
            # 格式化时间
            for investor_cap_time in investor["capital"]:
                investor_cap_time["time"] = to_date(investor_cap_time["time"]) if investor_cap_time.get("time") else ""
            for investor_cap_actl_time in investor["capital_actl"]:
                investor_cap_actl_time["time"] = to_date(investor_cap_actl_time["time"]) if investor_cap_actl_time.get(
                    "time") else ""

            formatted_investor = {
                'investor_name': investor['investor_name'],
                'investor_type': 0,
                'capital': investor["capital"],
                'capital_actl': investor["capital_actl"],
                'detail': {
                    'businessLicNo': investor["card_no"] if investor.get('card_no') else '',
                    'businessLicType': investor["card_type"] if investor.get('card_type') else '',
                    'shareholderName': investor["investor_name"] if investor.get('investor_name') else '',
                    'shareholderType': investor["investor_type"] if investor.get('investor_type') else '',
                }
            }
            msv_investor_set.add(str(formatted_investor))  # 用于查看是否有重复股东
            msv_investor_list.append(MSVInvestor.from_dict(formatted_investor))  # 收录股东，待写入多源多版本
            if len(msv_investor_set) != len(msv_investor_list):
                msv_investor_list.remove(MSVInvestor.from_dict(formatted_investor))  # 移除重复股东
        return msv_investor_list

    @staticmethod
    def write_msv_staff_info(staff_list: list) -> list:
        """将主要人员信息写入多源多版本记录。
        Args:
            staff_list (list): 主要人员信息列表。
        Returns:
            list: 写入后的主要人员信息列表。
        """
        msv_staff_set, msv_staff_list = set(), list()
        for staff in staff_list:
            msv_staff_set.add(json.dumps({
                'staff_name': staff['staff_name'],
                'staff_position': staff['staff_position']
            }, ensure_ascii=False))

            msv_staff_list.append(MSVStaff.from_dict({
                'staff_name': staff['staff_name'],
                'staff_position': staff['staff_position']
            }))

            if len(msv_staff_set) != len(msv_staff_list):  # 移除重复主要人员
                msv_staff_list.remove(MSVStaff.from_dict({
                    'staff_name': staff['staff_name'],
                    'staff_position': staff['staff_position']
                }))
        return msv_staff_list

    @staticmethod
    def write_msv_change_info(change_list: list) -> list:
        """将变更记录写入多源多版本记录。
        Args:
            change_list: 变更记录列表。
        Returns:
            list: 写入后的变更记录列表。
        """
        msv_change_list = list()
        for change_info in change_list:
            msv_change_list.append(MSVChange.from_dict(
                {
                    'change_item': change_info['change_item'],
                    'change_time': to_date(change_info['change_time']) if change_info.get("change_time") else "",
                    'content_before': change_info['content_before'],
                    'content_after': change_info['content_after']
                }
            ))
        return msv_change_list

    @staticmethod
    def write_msv_abnormal_info(abnormal_list: list) -> list:
        msv_abnormal_list = list()
        for abnormal_info in abnormal_list:
            msv_abnormal_list.append(MSVCompanyAbnormal.from_dict(
                {
                    'put_date': to_date(abnormal_info['put_date']),
                    'put_reason': abnormal_info['put_reason'],
                    'put_department': abnormal_info['put_department'],
                    'remove_date': to_date(abnormal_info['rem_date']),
                    'remove_reason': abnormal_info['rem_reason'],
                    'remove_department': abnormal_info['rem_department'],
                    'statistics_put_reason': abnormal_info['put_reason_type'],
                    'statistics_remove_reason': abnormal_info['rem_reason_type']
                }
            ))
        return msv_abnormal_list
