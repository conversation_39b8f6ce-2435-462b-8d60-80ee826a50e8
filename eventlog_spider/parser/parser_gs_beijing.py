# encoding=utf8
import re
from datetime import datetime

from resx.func import to_date
from biz_utils.msv_write import *
from eventlog_spider.common.eventlog import Spider<PERSON><PERSON>, EventlogOld as Eventlog
from eventlog_spider.parser.parser_gs import GSParserTask, GSParser
from biz_utils.entity.company import CompanyDao, Company
from resx.log import setup_logger

logger = setup_logger(name=__name__)


class BeijingParserTask(GSParserTask):
    pass


class BeiJingParser(GSParser):
    @classmethod
    def get_name(cls):
        return 'bj'

    def __init__(self):
        self.dao = CompanyDao()
        super().__init__(task_cls=BeijingParserTask, msv_source=MSVSource.BJ)

    def do_parse(self):
        task: BeijingParserTask = self.get_parser_task()
        eventlog: Eventlog = task.eventlog
        pages = task.pages
        keyword = eventlog.selector.get_info('keyword') or eventlog.selector.word

        company_info = pages.get('company_info.txt', None)
        if not company_info:
            logger.warning(f'no page base {eventlog}')
            eventlog.spider_code = SpiderCode.FAIL
            return
        company_info = json.loads(company_info)
        if not company_info[9]:
            logger.warning(f'没有照面信息 {eventlog}')
            eventlog.spider_code = SpiderCode.GIVE_UP
            return

        # 标记
        info_pre: Company = self.dao.get_by_id(eventlog.selector.word)
        approved_time_pre = info_pre.approved_date
        approved_time = datetime.strptime(company_info[9].replace('年', '-').replace('月', '-').replace('日', ''), "%Y-%m-%d").date()
        if approved_time > approved_time_pre:
            eventlog.spider.ab_info['time_changed'] = f"{approved_time_pre} --> {approved_time}"

        msv_base_info_data = dict(
            name=company_info[0],
            # legal_person_name=company_info[3],
            legal_person_name=re.sub(r'([(（]委派代表.*?[）)])$', '', company_info[3]),
            reg_number=company_info[-1],
            company_org_type=company_info[2],
            reg_location=company_info[11],
            estiblish_time=to_date(company_info[5]),
            approved_time=to_date(company_info[9]),
            from_time=to_date(company_info[6]),
            to_time=to_date(company_info[7]),
            business_scope=company_info[12],
            reg_institute=company_info[8],
            reg_status=company_info[10],
            reg_capital=company_info[4],
            credit_code=company_info[1],
        )
        msv_base_info = MSVBaseInfo.from_dict(msv_base_info_data)
        logger.info(f'{keyword} 基本信息解析完成')

        msv_partnerships = None
        company_org_type = msv_base_info.company_org_type
        legal_person_name = msv_base_info.legal_person_name
        if company_org_type and '合伙' in company_org_type and legal_person_name:
            legal_person_name = legal_person_name.replace('委托代表：', '')
            msv_partnerships = [MSVPartnership.from_dict(dict(executive=x)) for x in legal_person_name.split(',')]
        task.set_base(msv_base_info, msv_partnerships)

        msv_investors = [MSVInvestor.from_dict(
            {
                'investor_name': x['inv'],
                'investor_type': 0,
                'capital': [{
                    'amomon': f"{y['subconam']}万元",
                    'time': y['subcondate'],
                    'paymet': y['subconform']
                } for y in x['认缴']],
                'capital_actl': [{
                    'amomon': f"{z['subconam']}万元",
                    'time': z['subcondate'],
                    'paymet': z['subconform']
                } for z in x['实缴']],
                'detail': {
                    'amomon': x['认缴额（万元）'] + '万元' if x['认缴额（万元）'] else '',
                    # 'shareholder_type': x.get('invtype', ''),
                    '股东/合伙人类别': x.get('invtype', ''),
                    '实缴额（万元）': x['实缴额（万元）'] + '万元' if x['实缴额（万元）'] else '',
                    '证照/证件类型': x.get('blictype', ''),
                    '证照/证件编号': x.get('blicno', '')
                }
            }) for x in json.loads(pages['investor_info.txt'])]
        task.set_investors(msv_investors)
        logger.info(f'{keyword} 股东解析完成')

        msv_staff = [MSVStaff.from_dict({
            'staff_name': i['name'],
            'staff_position': i['position']
        }) for i in json.loads(pages['staff_info.txt'])]
        task.set_staffs(msv_staff)
        logger.info(f'{keyword} 主要人员解析完成')

        compile_ = re.compile(r'<.*?>')
        msv_change = [MSVChange.from_dict(
            {'change_item': re.sub(compile_, '', row['altitem']),
             'change_time': to_date(row.get('altdate', '')),
             'content_before': re.sub(compile_, '', row.get('altbe', '')),
             'content_after': re.sub(compile_, '', row.get('altaf', ''))}
        ) for row in json.loads(pages['change_info.txt'])]
        task.set_changes(msv_change)
        logger.info(f'{keyword} 变更信息解析完成')
