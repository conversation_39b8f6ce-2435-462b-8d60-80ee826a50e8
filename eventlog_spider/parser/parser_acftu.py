# encoding=utf8

import json
from eventlog_spider.common.eventlog import <PERSON><PERSON><PERSON>
from eventlog_spider.parser.parser import <PERSON><PERSON><PERSON><PERSON><PERSON>, Parser
from resx.mysql_dao import MySQLDao
from resx.config import *
from resx.log import setup_logger

logger = setup_logger(name=__name__)


class ACFTUParserTask(ParserTask):
    pass


class ACFTUParser(Parser):
    def __init__(self):
        self.dao = MySQLDao(db_tb_name='prism.organization_info', **CFG_MYSQL_GS_OUTER, primary_index_fields=(['unified_social_credit_code'], []))
        super().__init__(task_cls=ACFTUParserTask)

    @classmethod
    def get_name(cls):
        return 'acftu'

    def do_parse(self):
        task: ACFTUParserTask = self.get_parser_task()
        eventlog = task.eventlog

        detail_text = task.pages.get('detail.txt', None)
        if not detail_text:
            logger.warning(f'no detail_text {task.event_id}')
            eventlog.spider_code = SpiderCode.FAIL
            return

        d = json.loads(detail_text)
        data = d.get('data', {})
        if data['tyshxydm'] != eventlog.selector.word:
            logger.warning(f'word={eventlog.selector.word} credit_no={data["tyshxydm"]}')
            eventlog.spider_code = SpiderCode.FAIL
            return

        organization_info = {
            'unified_social_credit_code': data['tyshxydm'],
            'org_source': 'acftu',
        }
        if data['jgmc']:
            organization_info['org_name'] = data['jgmc']
        if data['jgdz']:
            organization_info['address'] = data['jgdz']
        if data["yxqxs"] and data["yxqxe"]:
            organization_info['expiry_date'] = f'{data["yxqxs"]} 至 {data["yxqxe"]}'
        if data['fddbr']:
            organization_info['legal_person'] = data['fddbr']
        if data['pzjgmc']:
            organization_info['registration_authority'] = data['pzjgmc']

        self.dao.save(organization_info)
