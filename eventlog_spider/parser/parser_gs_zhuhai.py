# encoding=utf8
import re

from resx.func import to_date
from biz_utils.msv_write import *
from eventlog_spider.common.eventlog import SpiderC<PERSON>, EventlogOld as Eventlog
from eventlog_spider.parser.parser_gs import G<PERSON>arserTask, GSParser
from resx.log import setup_logger

logger = setup_logger(name=__name__)


class ZhuhaiParserTask(GSParserTask):
    pass


class ZhuhaiParser(GSParser):

    @classmethod
    def get_name(cls):
        return 'gdzh'

    def __init__(self, ):
        super().__init__(task_cls=ZhuhaiParserTask, msv_source=MSVSource.GDZH)

    def do_parse(self):
        task: ZhuhaiParserTask = self.get_parser_task()
        eventlog: Eventlog = task.eventlog
        pages = task.pages
        keyword = eventlog.selector.get_info('keyword') or eventlog.selector.word

        base_info = pages.get('base_info.txt', None)
        if not base_info:
            logger.warning(f'no page base {eventlog}')
            eventlog.spider_code = SpiderCode.FAIL
            return

        base_info = json.loads(base_info)
        msv_base_info_data = dict(
            name=base_info['name'],
            # legal_person_name=base_info['legal_person_name'],
            legal_person_name=re.sub(r'([(（]委派代表.*?[）)])$', '', base_info['legal_person_name']),
            company_org_type=base_info['company_org_type'] if base_info['company_org_type'] != '' else None,
            reg_location=base_info.get('reg_location') if base_info.get('reg_location') != '' else None,
            estiblish_time=to_date(base_info.get('estiblish_time')),
            from_time=to_date(base_info.get('from_time')),
            to_time=to_date(base_info.get('to_time')) if to_date(base_info.get('to_time')) != '' else None,
            business_scope=base_info.get('business_scope').strip(),
            reg_status=base_info.get('reg_status') if base_info.get('reg_status') != '' else None,
            reg_capital=base_info.get('reg_capital') if base_info.get('reg_capital') != '' else None,
            credit_code=base_info.get('credit_code'),
            reg_number=base_info.get('reg_number'),
            approved_time=to_date(base_info.get('approved_time')),
            reg_institute=base_info.get('reg_institute')
        )
        msv_base_info = MSVBaseInfo.from_dict(msv_base_info_data)
        logger.info(f'{keyword} 基本信息解析完成')

        msv_partnerships = []
        company_org_type = msv_base_info.company_org_type
        legal_person_name = msv_base_info.legal_person_name
        if company_org_type and '合伙' in company_org_type and legal_person_name:
            msv_partnerships = list(MSVPartnership.from_dict(dict(executive=x)) for x in legal_person_name.split('、'))
        task.set_base(msv_base_info, msv_partnerships)

        msv_investors = [MSVInvestor.from_dict(
            {
                'investor_name': x['MC'],
                'investor_type': 0,
                'capital': [{
                    'amomon': f'{x["RJCZE"]}万元' if x["RJCZE"] != '' else '',
                    'time': x['CZQX'][:10] if x['CZQX'] is not None else '',
                    'paymet': x['RJCZFS']
                }]
            }) for x in json.loads(pages['investor_info.txt'])]
        task.set_investors(msv_investors)
        logger.info(f'{keyword} 股东解析完成')

        if pages.get('staff_info.txt'):
            msv_staff = [MSVStaff.from_dict({
                'staff_name': i['XM'],
                'staff_position': i['RYZT']
            }) for i in json.loads(pages['staff_info.txt'])]
            task.set_staffs(msv_staff)
        logger.info(f'{keyword} 主要人员解析完成')

        msv_change = [MSVChange.from_dict(
            {
                'change_item': row['AltItem'],
                'change_time': to_date(row['AltDate'].replace("T", ' ')),
                'content_before': row['AltBe'],
                'content_after': row['ALtAf']
            }
        ) for row in json.loads(pages['change_info.txt'])]
        task.set_changes(msv_change)
        logger.info(f'{keyword} 变更解析完成')
