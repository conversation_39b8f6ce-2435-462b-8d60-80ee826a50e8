import re
from resx.func import to_date
from biz_utils.msv_write import *
from eventlog_spider.common.eventlog import Spider<PERSON><PERSON>, EventlogOld as Eventlog
from eventlog_spider.parser.parser_gs import GSParserTask, GSParser
from resx.log import setup_logger

logger = setup_logger(name=__name__)


class ShanghaiParserTask(GSParserTask):
    pass


class ShanghaiParser(GSParser):
    @classmethod
    def get_name(cls):
        return 'sh'

    def __init__(self):
        super().__init__(task_cls=ShanghaiParserTask, msv_source=MSVSource.SH)

    def do_parse(self):
        task: ShanghaiParserTask = self.get_parser_task()
        eventlog = task.eventlog
        pages = task.pages
        keyword = eventlog.selector.get_info('keyword') or eventlog.selector.word

        base_info = pages.get('base_info', None)
        base_info = json.loads(base_info)
        if not base_info:
            logger.warning(f'no page base {eventlog}')
            eventlog.spider_code = SpiderCode.FAIL
            return

        company_org_type = None
        if '公司类型' in base_info:
            company_org_type = base_info['公司类型']
        elif '合伙类型' in base_info:
            company_org_type = base_info['合伙类型']
        elif '合伙企业类型' in base_info:
            company_org_type = base_info['合伙企业类型']

        to_time = base_info['营业期限'][11:] if '营业期限' in base_info else base_info.get('合伙期限', '')[11:]
        if to_time == "不约定期限":
            to_time = None

        legal_person_name = base_info['法定代表人'] if '法定代表人' in base_info else base_info['执行事务合伙人']
        msv_base_info_data = dict(
            name=base_info['名称'],
            legal_person_name=re.sub(r'([(（]委派代表.*?[）)])$', '', legal_person_name),
            reg_number=base_info.get('注册号', ''),
            company_org_type=company_org_type,
            reg_location=base_info.get('住所', '') or base_info.get('主要经营场所', ''),
            estiblish_time=to_date(base_info['成立日期']),
            # approved_time=to_date(base_info['apprDate']),
            from_time=base_info['营业期限'][:10] if '营业期限' in base_info else base_info.get('合伙期限', '')[:10],
            to_time=to_time,
            business_scope=base_info.get('经营范围', '').strip(),
            reg_institute=base_info.get('登记机关', ''),
            reg_status=base_info.get('企业状态', ''),
            reg_capital=base_info['注册资本'] if '注册资本' in base_info else base_info.get('出资额', ''),
            credit_code=base_info.get('统一社会信用代码', ''),
        )
        msv_base_info = MSVBaseInfo.from_dict(msv_base_info_data)
        logger.info(f'{keyword} 基本信息解析完成')

        msv_partnerships = None
        company_org_type = msv_base_info.company_org_type
        legal_person_name = msv_base_info.legal_person_name
        if company_org_type and '合伙' in company_org_type and legal_person_name:
            msv_partnerships = [MSVPartnership.from_dict(dict(executive=x)) for x in legal_person_name.split(',')]
        task.set_base(msv_base_info, msv_partnerships)

        msv_investors = [MSVInvestor.from_dict(
            {
                'investor_name': x,
                'investor_type': 0,

            }) for x in json.loads(pages['investor'])]
        task.set_investors(msv_investors)
        logger.info(f'{keyword} 股东解析完成')
