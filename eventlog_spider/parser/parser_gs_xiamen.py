import re

from resx.func import to_date
from biz_utils.msv_write import *
from eventlog_spider.common.eventlog import Spider<PERSON><PERSON>, EventlogOld as Eventlog
from eventlog_spider.parser.parser_gs import GSParserTask, GSParser
from resx.log import setup_logger

logger = setup_logger(name=__name__)


class XiamenParserTask(GSParserTask):
    pass


class XiamenParser(GSParser):
    @classmethod
    def get_name(cls):
        return 'fjxm'

    def __init__(self):
        super().__init__(task_cls=XiamenParserTask, msv_source=MSVSource.FJXM)

    def do_parse(self):
        task: XiamenParserTask = self.get_parser_task()
        eventlog: Eventlog = task.eventlog
        pages = task.pages
        keyword = eventlog.selector.get_info('keyword') or eventlog.selector.word

        base_info = pages.get('base_info.txt', None)
        if not base_info:
            logger.warning(f'no page base {eventlog}')
            eventlog.spider_code = SpiderCode.FAIL
            return

        base_info = json.loads(base_info)
        time_ = re.findall(r'(\d{4}-\d{2}-\d{2})', base_info.get('经营期限', ''))
        if not time_:
            from_time = to_date(base_info['成立日期'])
            to_time = None
        else:
            from_time = to_date(time_[0])
            to_time = to_date(time_[1])

        msv_base_info_data = dict(
            name=base_info['商事主体名称'],
            # legal_person_name=base_info['法定代表人'],
            legal_person_name=re.sub(r'([(（]委派代表.*?[）)])$', '', base_info['法定代表人']),
            # reg_number=base_info.get('regNo', None),
            company_org_type=re.search(r'【(.*?)】', base_info['商事主体类型']).group(1),
            reg_location=base_info.get('住所', ''),
            estiblish_time=to_date(base_info['成立日期']),
            # approved_time=to_date(base_info['apprDate']),
            from_time=from_time,
            to_time=to_time,
            business_scope=base_info.get('经营范围', '').strip(),
            # reg_institute=base_info.get('regOrg_CN', None),
            reg_status=base_info.get('主体状态', ''),
            reg_capital=base_info['认缴出资额'].replace(' ', '') if '认缴出资额' in base_info else '',
            credit_code=base_info['统一社会信用代码'],
        )
        msv_base_info = MSVBaseInfo.from_dict(msv_base_info_data)
        logger.info(f'{keyword} - 基本信息解析完成')

        msv_partnerships = None
        company_org_type = msv_base_info.company_org_type
        legal_person_name = msv_base_info.legal_person_name
        if company_org_type and '合伙' in company_org_type and legal_person_name:
            msv_partnerships = [MSVPartnership.from_dict(dict(executive=x)) for x in legal_person_name.split(',')]
        task.set_base(msv_base_info, msv_partnerships)

        msv_investors = [MSVInvestor.from_dict(
            {
                'investor_name': x['投资人'],
                'investor_type': 0,
                'capital': [{
                    'amomon': f'{x["认缴出资额（万）"]}万元' if x["认缴出资额（万）"] != '' else '',
                    'time': f"{x['缴资期限'][:4]}-{x['缴资期限'][4:6]}-{x['缴资期限'][6:]}" if (
                            x['缴资期限'] != '') else '',
                    'paymet': x['出资方式']
                }]
            }) for x in json.loads(pages['investor_info.txt'])]
        task.set_investors(msv_investors)
        logger.info(f'{keyword} 股东解析完成')

        msv_staff = [MSVStaff.from_dict({
            'staff_name': i['姓名'],
            'staff_position': i['职务']
        }) for i in json.loads(pages['staff_info.txt'])]
        task.set_staffs(msv_staff)
        logger.info(f'{keyword} 主要人员解析完成')
