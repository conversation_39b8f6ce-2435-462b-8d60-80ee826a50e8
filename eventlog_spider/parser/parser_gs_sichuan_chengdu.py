import re
from resx.func import to_date
from biz_utils.msv_write import *
from eventlog_spider.common.eventlog import Spider<PERSON><PERSON>, EventlogOld as Eventlog
from eventlog_spider.parser.parser_gs import GSParserTask, GSParser
from resx.log import setup_logger

logger = setup_logger(name=__name__)


class ChengduParserTask(GSParserTask):
    pass


class ChengduParser(GSParser):
    @classmethod
    def get_name(cls):
        return 'sccd'

    def __init__(self):
        super().__init__(task_cls=ChengduParserTask, msv_source=MSVSource.Chengdu)

    def do_parse(self):
        task: ChengduParserTask = self.get_parser_task()
        eventlog: Eventlog = task.eventlog
        pages = task.pages
        keyword = eventlog.selector.get_info('keyword') or eventlog.selector.word

        base_info = pages.get('base_info.txt')
        if not base_info:
            logger.warning(f'no page base {eventlog}')
            eventlog.spider_code = SpiderCode.FAIL
            return

        base_info = json.loads(base_info)
        logger.info(base_info)
        msv_base_info_data = dict(
            name=base_info['主体名称'],
            legal_person_name=re.sub(r'([(（]委派代表.*?[）)])$', '', base_info.get('corporate', '')),
            reg_number=base_info.get('reg_no', ''),
            company_org_type=self.replace_bracket(base_info.get('企业类型', '')),
            reg_location=base_info.get('住所（经营场所）', ''),
            estiblish_time=to_date(base_info.get('成立日期')),
            approved_time=to_date(base_info.get('核准日期')),
            from_time=to_date(self.get_value(base_info, '营业期限自')[:10]),
            to_time=to_date(self.get_value(base_info, '营业期限至')[:10]),
            business_scope=base_info.get('主营范围', '').strip(),
            reg_institute=base_info.get('登记机关'),
            reg_status=base_info.get('状态'),
            reg_capital=base_info.get('注册资本（金）'),
            credit_code=base_info['统一社会信用代码'],
        )
        msv_base_info = MSVBaseInfo.from_dict(msv_base_info_data)
        logger.info(f'{keyword} 基本信息解析完成')

        msv_partnerships = None
        company_org_type = msv_base_info.company_org_type
        legal_person_name = msv_base_info.legal_person_name
        if company_org_type and '合伙' in company_org_type and legal_person_name:
            msv_partnerships = [MSVPartnership.from_dict(dict(executive=x)) for x in legal_person_name.split(',')]
        task.set_base(msv_base_info, msv_partnerships)

        msv_investors = [MSVInvestor.from_dict(
            {
                'investor_name': self.option(x),
                'investor_type': 0,
                'capital': []
            }) for x in json.loads(pages.get('investor_info.txt', []))]
        task.set_investors(msv_investors)
        logger.info(f'{keyword} 股东信息解析完成')

        msv_staff = [MSVStaff.from_dict({
            'staff_name': i['name'],
            'staff_position': i.get('position') if i.get('position') else ''
        }) for i in json.loads(pages.get('staff_info.txt', []))]
        task.set_staffs(msv_staff)
        logger.info(f'{keyword} 主要人员解析完成')

    @staticmethod
    def replace_bracket(name, cn_to_en=False):
        if cn_to_en:
            return name.replace('（', '(').replace('）', ')')
        return name.replace('(', '（').replace(')', '）')

    @staticmethod
    def get_value(data, key):
        if data.get(key, ''):
            return data[key]
        return ''

    @staticmethod
    def option(data):
        name1 = data.get('姓名') if data.get('姓名') else ''
        name2 = data.get('合伙人') if data.get('合伙人') else ''
        name3 = data.get('投资人') if data.get('投资人') else ''
        if name1:
            return name1
        if name2:
            return name2
        if name3:
            return name3
        return ''
