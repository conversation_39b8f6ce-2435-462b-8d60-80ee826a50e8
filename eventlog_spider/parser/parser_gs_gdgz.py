import re
from resx.func import to_date
from biz_utils.msv_write import *
from eventlog_spider.common.eventlog import Spider<PERSON><PERSON>, EventlogOld as Eventlog
from eventlog_spider.parser.parser_gs import GSParserTask, GSParser
from resx.log import setup_logger

logger = setup_logger(name=__name__)


class GDGZParserTask(GSParserTask):
    pass


class GDGZParser(GSParser):
    @classmethod
    def get_name(cls):
        return 'gdgz'

    def __init__(self):
        super().__init__(task_cls=GDGZParserTask, msv_source=MSVSource.GZ)

    def do_parse(self):
        task: GDGZParserTask = self.get_parser_task()
        eventlog: Eventlog = task.eventlog
        base_info = task.pages.get('base_info.txt', None)
        keyword = eventlog.selector.get_info('keyword') or eventlog.selector.word

        if not base_info:
            logger.warning(f'no page base {eventlog}')
            eventlog.spider_code = SpiderCode.FAIL
            return
        base_info = json.loads(base_info)

        temp = {}
        for k, v in base_info.items():
            if '：' in k:
                temp[k.replace('：', '')] = v
        for k, v in temp.items():
            base_info[k] = v

        if '执行事务合伙人' in base_info:
            base_info['法定代表人'] = base_info['执行事务合伙人']
        if '经营者' in base_info:
            base_info['法定代表人'] = base_info['经营者']
        dates = re.findall(r'(\d{4}年\d{1,2}月\d{1,2}日)', base_info.get('营业期限'))
        msv_base_info_data = dict(
            name=base_info['名称'],
            legal_person_name=re.sub(r'([(（]委派代表.*?[）)])$', '', base_info['法定代表人']),
            reg_number=base_info.get('注册号'),
            company_org_type=base_info.get('商事主体类型'),
            reg_location=base_info.get('地址'),
            estiblish_time=to_date(base_info['成立日期']),
            approved_time=to_date(base_info['核准日期']),
            from_time=to_date(dates[0]) if dates else None,
            to_time=to_date(dates[1]) if len(dates) > 1 else None,
            business_scope=base_info.get('经营范围'),
            reg_institute=base_info.get('登记机关'),
            reg_status=base_info.get('主体状态'),
            reg_capital=base_info.get('注册资本'),
            credit_code=base_info.get('统一社会信用代码'),
        )
        msv_base_info = MSVBaseInfo.from_dict(msv_base_info_data)
        logger.info(f'{keyword} 基本信息解析完成')

        msv_partnerships = None
        company_org_type = msv_base_info.company_org_type
        legal_person_name = msv_base_info.legal_person_name
        if company_org_type and '合伙' in company_org_type and legal_person_name:
            msv_partnerships = [MSVPartnership.from_dict(dict(executive=x)) for x in legal_person_name.split(',')]
        task.set_base(msv_base_info, msv_partnerships)
