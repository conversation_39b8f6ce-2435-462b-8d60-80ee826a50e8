# encoding=utf8
from abc import abstractmethod
from threading import Lock, current_thread
from typing import Dict
import resx.func
from eventlog_spider.common.eventlog_unify import Eventlog, StatusCode
from eventlog_spider.common.obs_manager import OBSManager
from resx.log import setup_logger
from crawler_log.log_init_v2 import Log

logger = setup_logger(name=__name__)


class ParserTask(object):
    def __init__(self, eventlog: Eventlog, pages):
        self.eventlog: Eventlog = eventlog
        self.event_id = self.eventlog.event_id
        self.pages: Dict[str, str] = pages
        self.log_param_ctx: Dict[str, str] = {}
        keyword = eventlog.selector.get_info('keyword')
        self.log_param_ctx['keyword'] = keyword
        self.log_param_ctx['company'] = keyword
        if isinstance(logger, Log):
            logger.ParamManger().add_params(**self.log_param_ctx)


class Parser(object):
    def __init__(self, task_cls):
        self.task_cls = task_cls
        self.obs_manager = OBSManager()
        self.tasks: Dict[int, task_cls] = dict()
        self.tasks_lock = Lock()

    @classmethod
    @abstractmethod
    def get_name(cls):
        pass

    def parse(self, eventlog: Eventlog) -> Eventlog:
        logger.info(f'==== BEGIN {eventlog}')

        # filter bad eventlog
        if eventlog.code != StatusCode.GENERAL_ERROR_RETRY:
            return eventlog

        page_ts = eventlog.parser.data.get('page_ts', 0)
        if page_ts == 0:
            eventlog.code = StatusCode.GENERAL_ERROR_RETRY
            logger.warning(f'page_ts bad {eventlog.event_id}')
            return eventlog

        pages_obs_path = f'page/{self.get_name()}/{eventlog.selector.info["keyword"]}/{page_ts}'
        pages = self.obs_manager.download_pages(pages_obs_path)

        with self.tasks_lock:
            tid = current_thread().ident
            self.tasks[tid] = self.task_cls(eventlog, pages)
        self.do_parse()

        if eventlog.code == StatusCode.UNPROCESSED:
            self.post_parse()

        # 最后设置成功
        if eventlog.code == StatusCode.UNPROCESSED:
            eventlog.code = StatusCode.SUCCESS
        return eventlog

    @abstractmethod
    def do_parse(self):
        pass

    def post_parse(self):
        # task: ParserTask = self.get_parser_task()
        # if eventlog.is_clue:
        # eventlog.spider.item_insert 这个需要 业务自己处理 parser_gsxt已处理
        # 设置 eventlog.selector.info['entry_word'] 以及 eventlog.selector.info['entry_*']
        pass

    def get_parser_task(self):
        with self.tasks_lock:
            tid = current_thread().ident
            if tid not in self.tasks:
                raise RuntimeError(f'not task tid={tid}')
            return self.tasks[tid]
