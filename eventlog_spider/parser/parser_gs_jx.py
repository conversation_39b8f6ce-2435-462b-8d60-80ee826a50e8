# encoding=utf8
import re

from resx.func import to_date
from biz_utils.msv_write import *
from eventlog_spider.common.eventlog import SpiderC<PERSON>, EventlogOld as Eventlog
from eventlog_spider.parser.parser_gs import GSParserTask, GSParser
from resx.log import setup_logger

logger = setup_logger(name=__name__)


class GSJXParserTask(GSParserTask):
    pass


class GSJXParser(GSParser):
    def __init__(self):
        super().__init__(task_cls=GSJXParserTask, msv_source=MSVSource.JX)

    @classmethod
    def get_name(cls):
        return 'jx'

    def do_parse(self):
        task: GSJXParserTask = self.get_parser_task()
        eventlog: Eventlog = task.eventlog
        pages = task.pages
        keyword = eventlog.selector.get_info('keyword') or eventlog.selector.word

        company_info = pages.get('base_info.txt', None)
        if not company_info:
            logger.warning(f'no page base {eventlog}')
            eventlog.spider_code = SpiderCode.FAIL
            return

        company_info = json.loads(company_info)
        msv_base_info_data = dict(
            name=company_info['ENTNAME'],
            legal_person_name=re.sub(r'([(（]委派代表.*?[）)])$', '', company_info['NAME']),
            reg_number=company_info.get('REGNO', None),
            company_org_type=company_info.get('ENTTYPE_CN', None),
            reg_location=company_info.get('DOM', None),
            estiblish_time=to_date(company_info['ESTDATE']),
            approved_time=to_date(company_info['APPRDATE']),
            from_time=to_date(company_info.get('OPFROM', None)),
            to_time=to_date(company_info.get('OPTO', None).strip()),
            business_scope=company_info.get('OPSCOPE', ''),
            reg_institute=company_info.get('REGORG_CN', None),
            reg_status=company_info.get('REGSTATE_CN', None),
            reg_capital=str(company_info['REGCAP']) + "万" + (
                company_info.get('REGCAPCUR_CN', '人民币') if (company_info.get('REGCAPCUR_CN', '人民币') != ' ') else '人民币')
            if ('REGCAP' in company_info) else None,
            credit_code=company_info.get('UNISCID', None),
        )
        msv_base_info = MSVBaseInfo.from_dict(msv_base_info_data)
        logger.info(f'{keyword} 基本信息解析完成')

        msv_partnerships = None
        company_org_type = msv_base_info.company_org_type
        legal_person_name = msv_base_info.legal_person_name
        if company_org_type and '合伙' in company_org_type and legal_person_name:
            msv_partnerships = [MSVPartnership.from_dict(dict(executive=x)) for x in legal_person_name.split(',')]
        task.set_base(msv_base_info, msv_partnerships)

        msv_investors = [MSVInvestor.from_dict(
            {
                'investor_name': x['INV'],
                'investor_type': 0,
                'capital': x['认缴'],
                'capital_actl': x['实缴'],
                'detail': {
                    '股东类型': x['INVTYPE_CN'],
                    'shareholder_type': x['INVTYPE_CN']
                }
            }) for x in json.loads(pages['investor_info.txt'])]
        task.set_investors(msv_investors)
        logger.info(f'{keyword} - 股东解析完成')

        msv_staff = [MSVStaff.from_dict({
            'staff_name': i['NAME'],
            'staff_position': i['POSITION_CN']
        }) for i in json.loads(pages['staff_info.txt'])]
        task.set_staffs(msv_staff)
        logger.info(f'{keyword} - 主要人员解析完成')

        msv_change = [MSVChange.from_dict(
            {
                'change_item': row['ALTITEM_CN'],
                'change_time': to_date(row['ALTDATE']),
                'content_before': row.get('ALTBE', ''),
                'content_after': row.get('ALTAF', '')
            }
        ) for row in json.loads(pages['change_info.txt'])]
        task.set_changes(msv_change)
        logger.info(f'{keyword} - 变更信息解析完成')
