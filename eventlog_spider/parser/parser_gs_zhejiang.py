# encoding=utf8
import re

from resx.func import to_date
from biz_utils.msv_write import *
from eventlog_spider.common.eventlog import SpiderCode, EventlogOld as Eventlog
from eventlog_spider.parser.parser_gs import GSParserTask, GSParser
from resx.log import setup_logger

logger = setup_logger(name=__name__)


class ZJParserTask(GSParserTask):
    pass


class ZJParser(GSParser):
    def __init__(self):
        super().__init__(task_cls=ZJParserTask, msv_source=MSVSource.ZJ)

    @classmethod
    def get_name(cls):
        return 'zj'

    def do_parse(self):
        task: ZJParserTask = self.get_parser_task()
        eventlog: Eventlog = task.eventlog
        pages = task.pages
        keyword = eventlog.selector.get_info('keyword') or eventlog.selector.word

        company_info = json.loads(pages['company_info.txt'])
        msv_base_info_data = dict(
            name=company_info['企业名称'],
            legal_person_name=re.sub(r'([(（]委派代表.*?[）)])$', '', company_info.get('法定代表人', '') or company_info.get('执行事务合伙人', '')),
            reg_number=None,
            company_org_type=company_info.get('企业类型'),
            reg_location=company_info.get('住所'),
            estiblish_time=to_date(company_info['成立日期']),
            approved_time=to_date(company_info['核准日期']),
            from_time=to_date(company_info.get('营业日期自')),
            to_time=to_date(company_info.get('营业日期至')),
            business_scope=company_info.get('经营范围'),
            reg_institute=company_info.get('登记机关'),
            reg_status=company_info.get('登记状态'),
            reg_capital=company_info.get('注册资本'),
            credit_code=company_info.get('统一社会信用代码'),
        )
        msv_base_info = MSVBaseInfo.from_dict(msv_base_info_data)
        logger.info(f'{keyword} 基本信息解析完成')

        msv_partnerships = None
        company_org_type = msv_base_info.company_org_type
        legal_person_name = msv_base_info.legal_person_name
        if company_org_type and '合伙' in company_org_type and legal_person_name:
            msv_partnerships = [MSVPartnership.from_dict(dict(executive=x)) for x in legal_person_name.split(',')]
        task.set_base(msv_base_info, msv_partnerships)

        msv_investors = [MSVInvestor.from_dict(
            {
                'investor_name': x.get('股东名称', '') or x.get('合伙人名称', '') or x.get('发起人名称', ''),
                'investor_type': 0,
                'capital': [],
                'capital_actl': [],
                'detail': {
                    '股东类型': x.get('股东类型', '') or x.get('合伙人类型', '') or x.get('发起人类型', ''),
                }
            }) for x in json.loads(pages['investor_info.txt'])]
        task.set_investors(msv_investors)
        logger.info(f'{keyword} - 股东解析完成')

        msv_staff = [MSVStaff.from_dict({
            'staff_name': i['姓名'],
            'staff_position': i['职位']
        }) for i in json.loads(pages['staff_info.txt'])]
        task.set_staffs(msv_staff)
        logger.info(f'{keyword} - 主要人员解析完成')

        msv_change = [MSVChange.from_dict(
            {
                'change_item': row['变更事项'],
                'change_time': to_date(row.get('变更日期', '')),
                'content_before': row.get('变更前内容', ''),
                'content_after': row.get('变更后内容', '')
            }
        ) for row in json.loads(pages['change_info.txt'])]
        task.set_changes(msv_change)
        logger.info(f'{keyword} 变更信息解析完成')
