import requests
from loguru import logger
import urllib3
import time
import uuid
import random
import execjs
import json
from requests import Session
import os
from requests.adapters import <PERSON><PERSON><PERSON><PERSON>pter
from typing import Union
from bs4 import BeautifulSoup
import re
from Crypto.Cipher import AES

from eventlog_spider.scripts.dianxuan.main import main_bj
# from eventlog_spider.scripts.slider.main import web
# from dao.cods.cods import Cods<PERSON>ao, AccountDao
from eventlog_spider.scripts.AES import AESCryptor
from eventlog_spider.scripts.nine.main import main

urllib3.disable_warnings()
headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* "
                  "Safari/537.36",
}
headers2 = {
    "Referer": "https://ss.cods.org.cn/latest/searchR",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* "
                  "Safari/537.36 Edg/*********",
}
headers3 = {
    "Origin": "https://ss.cods.org.cn",
    "Referer": "https://ss.cods.org.cn/latest/searchR",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
}
headers4 = {
    "Referer": "https://www.cods.org.cn/",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
}
headers5 = {
    "Origin": "https://www.cods.org.cn",
    "Referer": "https://www.cods.org.cn/cods/member/login?SiteID=1&Referer=https%3A%2F%2Fwww.cods.org.cn%2F",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
}
timeout = 10


def index(session: Session):
    url = "https://ss.cods.org.cn/latest/searchR"
    response = request(session, "GET", url, headers=headers, name='index')


def last_link(session: Session):
    url = "https://ss.cods.org.cn/latest/searchR"
    params = {"q": "", "currentPage": "1", "t": "common", "searchToken": ""}
    response = request(session, "GET", url, headers=headers2, params=params, name='last_link')
    logger.info(session.cookies.get_dict())
    return session.cookies.get_dict()


def get_challenge_gt(session: Session):
    url = "https://ss.cods.org.cn/gc/geetest/login"
    response = request(session, "GET", url, headers=headers2, params={"t": str(int(time.time() * 1000))}, tojson=True, name='get_challenge_gt')
    logger.info(response)
    return json.loads(response)


def validate_login(session: Session, user_name, user_power, challenge, validate):
    url = "https://ss.cods.org.cn/usercenter/validateLogin"
    data = {
        "user_name": user_name,
        "user_power": SM2_encrypt(user_power),
        "geetest_challenge": f"{challenge}",
        "geetest_validate": f"{validate}",
        "geetest_seccode": f"{validate}|jordan"
    }
    response = request(session, "POST", url, headers=headers3, data=data, name='validate_login', tojson=True)
    logger.info(response)
    return response


def SM2_encrypt(value):
    js_code = open(f'{os.path.dirname(os.path.abspath(__file__))}/sm2.js', 'r', encoding='utf-8').read()
    pubkeyHex = "04a494ef2071cce6524c6761dc6853a7024d023cd5169fbe2e3a3e2914f3febb15617058e90c5dc1adde01eb718e69b47b54819dc1810770bab8f8c4095b2d4178"
    js_compile = execjs.compile(js_code)
    result = js_compile.call('sm2Encrypt', value, pubkeyHex, 0)
    return result


def get_cookies(name='19280178513', password='1qaz2wsx'):
    session = requests.session()
    session.proxies = {'http': 'http://************:30636', 'https': 'http://************:30636'}
    session.mount('http://', HTTPAdapter(max_retries=1))
    session.mount('https://', HTTPAdapter(max_retries=1))

    for _ in range(10):
        try:
            index(session)
            challenge_gt = get_challenge_gt(session)

            # validate, _ = geetest_verify(challenge_gt['gt'], challenge_gt['challenge'])
            # validate, _ = main(challenge_gt)
            validate, _ = main_bj(challenge_gt)

            code_ = validate_login(session, name, password, challenge_gt['challenge'], validate)
            return code_['code'], last_link(session)
        except Exception as e:
            logger.warning(f'获取cookies失败 {e}')
    return 3, {}


def geetest_verify(gt, challenge):
    url = "http://10.99.193.106:8778/get_validate_v3"
    data = {"gt": gt, "challenge": challenge, "refer": "https://www.cods.org.cn/cods/member/login?SiteID=1&Referer=https%3A%2F%2Fwww.cods.org.cn%2F"}
    response = requests.post(url, json=data, timeout=30)
    logger.info(response.json())
    return response.json()['validate'], challenge


def request(session: Session, method: str, url: str, headers: dict = None, params: dict = None, data: dict = None,
            json: dict = None, path: str = None, name: str = '', tojson=False) -> Union[dict, str]:
    for i in range(5):
        response = None
        try:
            a = time.time()
            response = session.request(**{
                'method': method,
                'url': url,
                'data': data,
                'headers': headers,
                'verify': False,
                'timeout': timeout,
                'params': params,
                'json': json,
                # 'proxies': get_long_proxy()
            })

            status = response.status_code
            if status != 200:
                logger.warning(f'{name} {i} --> {status}')
                del session.cookies['proxyBase']
                continue

            logger.success(f'{name} --> {response.status_code} {time.time() - a}')
            if name in ['get_challenge_gt2']:
                a = response.text.replace("\n", "").replace('\r', '').replace('\t', '')
                logger.info(f'{name} --> {a}')

            if tojson:
                return response.json()
            return response.text

        except (requests.exceptions.ConnectionError, requests.exceptions.Timeout) as e:
            logger.warning(f'continue{i} exception: {e}')
            del session.cookies['proxyBase']
            continue
        except Exception as e:
            status = response.status_code if response else "空"
            text = response.text if response else "空"
            logger.warning(f'continue{i} 状态码：{status} res: {text} exception: {e}')
            del session.cookies['proxyBase']
            continue


def parse_html(html):
    soup = BeautifulSoup(html, 'lxml')
    uls = soup.find_all('ul')
    keys = []
    values: list = []
    for ul in [uls[3], uls[4], uls[5]]:
        for li in ul.find_all('li'):
            key_value = list(li.stripped_strings)[:2]
            if key_value:
                keys.append(key_value[0])
                values.append(key_value[1] if len(key_value) > 1 else '')

    keys.append('status')
    status = soup.select('em.tag.green')
    status2 = soup.select('em.tag.red')
    values.append(status[0].text[3:] if status else (status2[0].text[3:] if status2 else '未公示'))

    keys.append('最后更新日期')
    date = soup.select('em.tag.blue')
    values.append(date[0].text[-10:] if date else '0001-01-01')

    keys.append('name')
    values.append(list(soup.select_one('div.summary_info>h3').stripped_strings)[0])
    data = dict(zip(keys, values))
    return data


def test(cookies):
    url = "https://ss.cods.org.cn/latest/detail"
    params = {"jgdm": "e14fe6ef45e43260ad038594b66a6417"}

    session = requests.session()
    session.cookies.update(cookies)
    response = session.get(url, headers=headers, params=params, proxies={
        'http': 'http://************:30636',
        'https': 'http://************:30636'
    }, verify=False, timeout=timeout)
    try:
        logger.info(parse_html(response.text))
        return True
    except Exception as e:
        pass


def get_long_proxy():
    res = requests.get('http://*************:8015/long-proxy')
    proxy = random.choice(res.text.split('\r\n'))
    return {
        'http': f'http://{proxy}',
        'https': f'http://{proxy}'
    }


if __name__ == '__main__':
    _, a = get_cookies()
    test(a)
    #
