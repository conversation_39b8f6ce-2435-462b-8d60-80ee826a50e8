import asyncio
import io
import os
from concurrent.futures import ProcessPoolExecutor

import uvicorn
from PIL import Image
from fastapi import Fast<PERSON><PERSON>, File, UploadFile
from ultralytics import YOLO


class PredictByClassify:

    def __init__(self):
        self.model = YOLO(os.path.join(os.path.dirname(__file__), 'yolo_jy_char_ocr.onnx'), task='classify')

    def predict(self, images: list[bytes]):
        pic_classify = []
        images = [Image.open(io.BytesIO(image)) for image in images]
        results = self.model.predict(images, imgsz=64, verbose=False)
        for result in results:
            predicted_class = result.names[result.probs.top1]
            pic_classify.append({predicted_class: float(result.probs.top1conf)})
        return pic_classify


predictor: PredictByClassify


def init_predictor():
    """Initialize the predictor once per process."""
    global predictor
    predictor = PredictByClassify()


def run_predictor_in_process(images: list[bytes]):
    """Run the predictor inference in a separate process using the per-process predictor."""
    global predictor
    if predictor is None:
        raise RuntimeError("Predictor not initialized in this process.")
    result = predictor.predict(images)
    return result


app = FastAPI()

# Create a fixed-size pool of workers, each running init_predictor once
executor = ProcessPoolExecutor(
    max_workers=10,
    initializer=init_predictor
)


@app.post("/char_ocr/")
async def char_ocr_endpoint(images: list[UploadFile] = File(...)):
    """Handle character OCR requests by offloading processing to a process pool."""
    # Read the uploaded image content
    image_contents = []
    for image in images:
        content = await image.read()
        image_contents.append(content)

    # Get the current asyncio event loop
    loop = asyncio.get_event_loop()

    # Run the predictor function in the process pool
    try:
        result = await loop.run_in_executor(executor, run_predictor_in_process, image_contents)
    except Exception as e:
        return []

    return result


if __name__ == '__main__':
    uvicorn.run(app, host="127.0.0.1", port=8000)
