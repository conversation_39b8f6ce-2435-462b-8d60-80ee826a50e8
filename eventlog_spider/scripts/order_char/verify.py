import requests
from loguru import logger
import time


def click_verify(image):
    try:
        a = time.time()
        # files = {'image': ('image.png', image, 'image/png')}
        res = requests.post('http://127.0.0.1:6666/order_char', files={'image': image}, timeout=5)
        logger.info(f'识别耗时：{time.time() - a}')
        return res.json()
    except requests.exceptions.Timeout:
        logger.warning("-------------------------------------------------------------------------------\n"
                       ""
                       "验证码识别超时5s\n"
                       ""
                       "-------------------------------------------------------------------------------")
        raise Exception("验证码识别超时5s")


if __name__ == '__main__':
    pass
