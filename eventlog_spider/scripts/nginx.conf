user work;
worker_processes  auto; #设置与服务器CPU核心数一致, 也可以使用 auto

error_log  /home/<USER>/ding/nginx/logs/error.log crit; #日志位置和级别

#pid        logs/nginx.pid;

events {
    worker_connections  1024;
}


http {
    include       mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                     '$status $body_bytes_sent "$http_referer" '
                     '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /home/<USER>/ding/nginx/logs/access.log  main;

    sendfile        on;
    tcp_nopush      on;
    tcp_nodelay     on;

    #keepalive_timeout  0;
    keepalive_timeout  65;

    gzip  on;

    upstream myapp {
        server 127.0.0.1:1402;
        server *************:1403;
    }

    # HTTP 服务
    server {
        listen       1401;
        server_name  *************; #重点1,修改为主机名或域名
        #rewrite ^(.*)$ https://$server_name$1 permanent; #将所有HTTP请求通过rewrite指令重定向到 HTTPS

        #charset koi8-r;
        #access_log  logs/host.access.log  main;

        location / {
            root   html; #重点2,如要自定义路径请修改，默认是nginx/html/
            index  index.html index.htm; #根索引文件，也就是输入ip或域名后在浏览器访问的第一页面
            proxy_pass http://myapp;
            proxy_redirect off;
            proxy_set_header Host $host:1401;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        error_page  404 /404.html;

        # redirect server error pages to the static page /50x.html
        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
    }
}