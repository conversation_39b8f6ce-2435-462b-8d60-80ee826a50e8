import time
from loguru import logger
import urllib3
import requests
import json
import re
from eventlog_spider.scripts.dianxuan.verify import YdmVerify
import cv2
import numpy as np
import execjs
from requests import Session
import os
from requests.adapters import HTTPAdapter
from typing import Union
from PIL import Image
from io import BytesIO

urllib3.disable_warnings()

headers = {
    "authority": "www.geetest.com",
    "accept": "application/json, text/javascript, */*; q=0.01",
    "accept-language": "zh-CN,zh;q=0.9",
    "cache-control": "no-cache",
    "pragma": "no-cache",
    "referer": "https://www.geetest.com/demo/click-float.html",
    "sec-ch-ua": '"Google Chrome";v="119", "Chromium";v="119", "Not?A_Brand";v="24"',
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "Windows",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "x-requested-with": "XMLHttpRequest"
}

headers2 = {
    "Host": "api.geetest.com",
    # "Pragma": "no-cache",
    # "Cache-Control": "no-cache",
    # "sec-ch-ua": "Google Chrome;v=119, Chromium;v=119, Not?A_Brand;v=24",
    # "sec-ch-ua-mobile": "?0",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) "
                  "Chrome/********* Safari/537.36",
    'referer': 'https://ss.cods.org.cn/'
    # "sec-ch-ua-platform": "Windows",
    # "Accept": "*/*",
    # "Sec-Fetch-Site": "cross-site",
    # "Sec-Fetch-Mode": "no-cors",
    # "Sec-Fetch-Dest": "script",
    # "Referer": "http://qyxy.scjgj.beijing.gov.cn/",
    # "origin": 'http://qyxy.scjgj.beijing.gov.cn',
    # "Accept-Language": "zh-CN,zh;q=0.9"
}
timeout = 3


def request(session: Session, method: str, url: str, headers: dict = None, params: dict = None, data: dict = None,
            json: dict = None, path: str = None, name: str = '', tojson=False, original=False) -> Union[dict, str, requests.Response]:
    for i in range(5):
        response = None
        try:
            a = time.time()
            response = session.request(**{
                'method': method,
                'url': url,
                'data': data,
                'headers': headers,
                'verify': False,
                'timeout': timeout,
                'params': params,
                'json': json
            })

            status = response.status_code
            if status != 200:
                logger.warning(f'{name} {i} --> {status}')
                del session.cookies['proxyBase']
                continue

            if original:
                return response

            # logger.success(f'{name} --> {response.status_code} --> time: {time.time() - a}')
            # a = response.text.replace("\n", "").replace('\r', '').replace('\t', '')
            # logger.info(f'{name} --> {a}')

            if tojson:
                return response.json()
            return response.text

        except (requests.exceptions.ConnectionError, requests.exceptions.Timeout) as e:
            logger.warning(f'continue{i} exception: {e}')
            del session.cookies['proxyBase']
            continue
        except Exception as e:
            status = response.status_code if response else "空"
            text = response.text if response else "空"
            logger.warning(f'continue{i} 状态码：{status} res: {text} exception: {e}')
            del session.cookies['proxyBase']
            continue


def register():
    """
    获取 challenge、gt
    """
    url = "https://www.geetest.com/demo/gt/register-click"
    params = {
        "t": str(int(time.time() * 1000))
    }
    response = requests.get(url, headers=headers, params=params, verify=False, timeout=2)

    # logger.success(response.status_code)
    # logger.info(response.json())

    return response.json()


def gettype(register_json, session: Session):
    url = "https://api.geetest.com/gettype.php"
    params = {
        "gt": register_json['gt'],
        "callback": f"geetest_{int(time.time() * 1000)}"
    }
    response = request(session, 'GET', url, headers=headers2, params=params, name='gettype')
    res = json.loads(re.search(r'geetest_\d{13}\((.*)\)', response).group(1))

    return res


def get1(register_json, session: Session):
    url = 'https://api.geetest.com/get.php'
    params = {
        "gt": register_json['gt'],
        "challenge": register_json['challenge'],
        "lang": "zh-cn",
        "pt": "0",
        "client_type": "web",
        "w": "",
        "callback": f"geetest_{int(time.time() * 1000)}"
    }

    response = request(session, 'GET', url, headers=headers2, params=params, name='get1')
    res = json.loads(re.search(r'geetest_\d{13}\((.*)\)', response).group(1))

    return res


def ajax1(register_json, session: Session):
    url = "https://api.geevisit.com/ajax.php"
    params = {
        "gt": register_json['gt'],
        "challenge": register_json['challenge'],
        "lang": "zh-cn",
        "pt": "0",
        "client_type": "web",
        "w": "",
        "callback": f"geetest_{int(time.time() * 1000)}"
    }

    response = request(session, 'GET', url, headers=headers2, params=params, name='ajax1')
    res = json.loads(re.search(r'geetest_\d{13}\((.*)\)', response).group(1))

    return res


def get2(register_json, ajax1_json, session: Session):
    url = "https://api.geevisit.com/get.php"
    params = {
        "is_next": "true",
        "type": ajax1_json['data']['result'],
        "gt": register_json['gt'],
        "challenge": register_json['challenge'],
        "lang": "zh-cn",
        "https": "true",
        "protocol": "https://",
        "offline": "false",
        "product": "float",
        "api_server": "api.geevisit.com",
        "isPC": "true",
        "autoReset": "true",
        "width": "100%",
        "callback": f"geetest_{int(time.time() * 1000)}"
    }

    response = request(session, 'GET', url, headers=headers2, params=params, name='get2')
    res = json.loads(re.search(r'geetest_\d{13}\((.*)\)', response).group(1))

    return res


def get_pic(url, register_json, session: Session):
    params = {"challenge": register_json['challenge']}

    for _ in range(5):
        content = request(session, 'GET', url, params=params, headers={
            "Host": "static.geetest.com",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) "
                          "Chrome/********* Safari/537.36",
            "referer": "https://ss.cods.org.cn/",
        }, original=True).content
        try:
            Image.open(BytesIO(content))
            return content
        except Exception as e:
            logger.warning(f'图片异常({_ + 1})：{e}')
            del session.cookies['proxyBase']
            continue

    return Exception('图片异常')


def verify(image: bytes):
    y = YdmVerify()
    verify_result = y.click_verify(image)

    # img = cv2.imdecode(np.frombuffer(image, np.uint8), cv2.IMREAD_COLOR)
    # for index, i in enumerate(verify_result):
    #     cv2.circle(img, (i[0], i[1]), 25, (0, 255, 0), thickness=2)
    #     cv2.putText(img, str(index + 1), (i[0] - 5, i[1] + 5), fontFace=cv2.FONT_HERSHEY_SCRIPT_SIMPLEX,
    #                 fontScale=1, color=(0, 0, 0), thickness=2)
    # cv2.imwrite(r'C:\Users\<USER>\Downloads\pygs-work-parent\temp\test.png', img)

    position = []
    for i in verify_result:
        x = round((i[0] / 344) * 10000)
        y = round((i[1] / 344) * 10000)
        position.append(f"{x}_{y}")

    return ','.join(position), verify_result


def get_gct_js_code(url, session: Session):
    js_code = request(session, 'GET', url, headers={
        "Host": "static.geetest.com",
        "referer": "https://ss.cods.org.cn/",
        "origin": "https://ss.cods.org.cn",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) "
                      "Chrome/********* Safari/537.36",
    }, name='get_gct_js_code')
    gct = re.findall(r'\[0];(function [a-zA-Z]{4}\([a-zA-Z]{1}\){var .*;break;}}})(function [a-zA-Z]{4}\([a-zA-Z]{1}\){var .*\(\);break;}}})return function\(',
                     js_code)[0]

    return gct[0], gct[1]


def get_w(position_str, get2_json, register_json, position, code1, code2):
    c_ = get2_json['data']['c']
    s = get2_json['data']['s']
    pic_url = get2_json['data']['pic']

    gt = register_json['gt']
    challenge = register_json['challenge']

    js_code = open(f'{os.path.dirname(os.path.abspath(__file__))}/w2.js', 'r', encoding='utf-8').read()
    js_compile = execjs.compile(js_code)
    w_params = js_compile.call('get_w', position_str, gt, challenge, pic_url, c_, s, position, code1, code2)

    return w_params['w']


def ajax2(get2_json, register_json, session: Session):
    pic_type = get2_json['data']['pic_type']

    pic = 'https://static.geetest.com' + get2_json['data']['pic']
    pic_content = get_pic(pic, register_json, session)

    gct_url = 'https://static.geetest.com' + get2_json['data']['gct_path']
    code1, code2 = get_gct_js_code(gct_url, session)

    if pic_type == 'word':
        a = time.time()
        position_str, position = verify(pic_content)
        b = time.time()
        w = get_w(position_str, get2_json, register_json, position, code1, code2)

        if b - a < 1.2:
            t = 1.2 - (b - a)
            time.sleep(t)

        url = 'https://api.geevisit.com/ajax.php'
        params = {
            "gt": register_json['gt'],
            "challenge": register_json['challenge'],
            "lang": "zh-cn",
            "pt": "0",
            "client_type": "web",
            "w": w,
            "callback": f"geetest_{int(time.time() * 1000)}"
        }
        response = request(session, 'GET', url, headers=headers2, params=params, name='ajax2')

        logger.info(json.loads(re.search(r'geetest_\d{13}\((.*)\)', response).group(1)))
        return json.loads(re.search(r'geetest_\d{13}\((.*)\)', response).group(1))
    else:
        logger.warning('其他验证码类型')


def main_bj(a, proxyBase=None, chrome=None):
    session = requests.session()
    session.proxies = {
        'http': 'http://10.99.138.95:30636',
        'https': 'http://10.99.138.95:30636'
    }
    session.mount('http://', HTTPAdapter(max_retries=2))
    session.mount('https://', HTTPAdapter(max_retries=2))

    gettype(a, session)

    get1(a, session)
    c = ajax1(a, session)

    d = get2(a, c, session)

    result = ajax2(d, a, session)

    return result['data']['validate'], a['challenge']


if __name__ == '__main__':
    pass