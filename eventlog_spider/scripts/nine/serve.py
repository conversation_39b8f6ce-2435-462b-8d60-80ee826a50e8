from fastapi import FastAP<PERSON>, UploadFile
import uvicorn
from typing import List

from classify import PredictByClassify

# from siamese import SiamOnnx

app = FastAPI()
classify = PredictByClassify()


# siamese = SiamOnnx()


@app.get('/')
def hello_word():
    return "极验3九宫格识别"


@app.post('/nine_cls')
def ocr(files: List[UploadFile]):
    pic: bytes = files[0].file.read()
    return classify.predict(pic)


# @app.post('/nine_sia')
# def ocr(files: List[UploadFile]):
#     pic: bytes = files[0].file.read()
#     return siamese.predict(pic)


if __name__ == '__main__':
    # gunicorn serve:app --workers 4 --worker-class uvicorn.workers.UvicornWorker --bind 0.0.0.0:1402 > 1.log  2>&1 &
    uvicorn.run("__main__:app", host="0.0.0.0", port=6666)
