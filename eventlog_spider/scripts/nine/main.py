import time
from loguru import logger
import urllib3
import requests
import json
import re
import cv2
import numpy as np
import execjs
from requests import Session
import os
from requests.adapters import HTT<PERSON>dapter
from typing import Union
from PIL import Image
from io import BytesIO
from hashlib import md5

from eventlog_spider.scripts.nine.number_to_coordinate import number_to_coordinates
from eventlog_spider.scripts.nine.verify import click_verify

urllib3.disable_warnings()

headers = {
    "Accept": "application/json, text/javascript, */*; q=0.01",
    "Accept-Language": "zh-CN,zh;q=0.9",
    "Cache-Control": "no-cache",
    "Connection": "keep-alive",
    "Pragma": "no-cache",
    "Referer": "https://ss.cods.org.cn/latest/searchR",
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "same-origin",
    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "X-Requested-With": "XMLHttpRequest",
    "sec-ch-ua": "\"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"macOS\""
}
timeout = 3


def request(session: Session, method: str, url: str, headers: dict = None, params: dict = None, data: dict = None,
            json: dict = None, path: str = None, name: str = '', tojson=False, original=False) -> Union[dict, str, requests.Response, None]:
    for i in range(5):
        response = None
        try:
            a = time.time()
            response = session.request(**{
                'method': method,
                'url': url,
                'data': data,
                'headers': headers,
                'verify': False,
                'timeout': timeout,
                'params': params,
                'json': json
            })

            status = response.status_code
            if status != 200:
                logger.warning(f'{name} {i} --> {status}')
                del session.cookies['proxyBase']
                continue

            if original:
                return response

            # logger.success(f'{name} --> {response.status_code} --> time: {time.time() - a}')
            # a = response.text.replace("\n", "").replace('\r', '').replace('\t', '')
            # logger.info(f'{name} --> {a}')

            if tojson:
                return response.json()
            return response.text
        except (requests.exceptions.ConnectionError, requests.exceptions.Timeout) as e:
            logger.warning(f'continue{i} exception: {e}')
            del session.cookies['proxyBase']
            continue
        except Exception as e:
            status = response.status_code if response else "空"
            text = response.text if response else "空"
            logger.warning(f'continue{i} 状态码：{status} res: {text} exception: {e}')
            del session.cookies['proxyBase']
            continue


def register():
    """
    获取 challenge、gt
    """
    url = "https://ss.cods.org.cn/gc/geetest/login"
    params = {"t": str(int(time.time() * 1000))}
    response = requests.get(url, headers=headers, params=params, verify=False, timeout=2)
    return response.json()


def gettype(register_json, session: Session):
    url = "https://api.geetest.com/gettype.php"
    params = {
        "gt": register_json['gt'],
        "callback": f"geetest_{int(time.time() * 1000)}"
    }
    response = request(session, 'GET', url, headers=headers, params=params, name='gettype')
    res = json.loads(re.search(r'geetest_\d{13}\((.*)\)', response).group(1))

    return res


def get1(register_json, session: Session):
    url = 'https://api.geetest.com/get.php'
    params = {
        "gt": register_json['gt'],
        "challenge": register_json['challenge'],
        "lang": "zh-cn",
        "pt": "0",
        "client_type": "web",
        "w": "",
        "callback": f"geetest_{int(time.time() * 1000)}"
    }

    response = request(session, 'GET', url, headers=headers, params=params, name='get1')
    res = json.loads(re.search(r'geetest_\d{13}\((.*)\)', response).group(1))

    return res


def ajax1(register_json, session: Session):
    url = "https://api.geevisit.com/ajax.php"
    params = {
        "gt": register_json['gt'],
        "challenge": register_json['challenge'],
        "lang": "zh-cn",
        "pt": "0",
        "client_type": "web",
        "w": "",
        "callback": f"geetest_{int(time.time() * 1000)}"
    }

    response = request(session, 'GET', url, headers=headers, params=params, name='ajax1')
    res = json.loads(re.search(r'geetest_\d{13}\((.*)\)', response).group(1))

    return res


def get2(register_json, ajax1_json, session: Session):
    url = "https://api.geevisit.com/get.php"
    params = {
        "is_next": "true",
        "type": ajax1_json['data']['result'],
        "gt": register_json['gt'],
        "challenge": register_json['challenge'],
        "lang": "zh-cn",
        "https": "true",
        "protocol": "https://",
        "offline": "false",
        "product": "float",
        "api_server": "api.geevisit.com",
        "isPC": "true",
        "autoReset": "true",
        "width": "100%",
        "callback": f"geetest_{int(time.time() * 1000)}"
    }

    response = request(session, 'GET', url, headers=headers, params=params, name='get2')
    res = json.loads(re.search(r'geetest_\d{13}\((.*)\)', response).group(1))
    return res


def get_pic(url, register_json, session: Session):
    params = {"challenge": register_json['challenge']}

    for _ in range(5):
        content = request(session, 'GET', url, params=params, headers={
            "Host": "static.geetest.com",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) "
                          "Chrome/********* Safari/537.36",
            "referer": "https://ss.cods.org.cn/",
        }, original=True).content
        try:
            Image.open(BytesIO(content))
            return content
        except Exception as e:
            logger.warning(f'图片异常({_ + 1})：{e}')
            del session.cookies['proxyBase']
            continue

    return Exception('图片异常')


def verify(image: bytes):
    verify_result = click_verify(image)
    position = []
    for i in verify_result:
        x, y = number_to_coordinates(i)
        position.append(f"{x}_{y}")
    return ','.join(position), verify_result


def get_gct_js_code(url, session: Session):
    js_code = request(session, 'GET', url, headers={
        "Host": "static.geetest.com",
        "referer": "https://ss.cods.org.cn/",
        "origin": "https://ss.cods.org.cn",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) "
                      "Chrome/********* Safari/537.36",
    }, name='get_gct_js_code')
    gct = re.findall(
        r'\[0];(function [a-zA-Z]{4}\([a-zA-Z]{1}\){var .*;break;}}})(function [a-zA-Z]{4}\([a-zA-Z]{1}\){var .*\(\);break;}}})return function\(',
        js_code)[0]

    return gct[0], gct[1]


def get_w(position_str, get2_json, register_json, position, code1, code2):
    c_ = get2_json['data']['c']
    s = get2_json['data']['s']
    pic_url = get2_json['data']['pic']

    gt = register_json['gt']
    challenge = register_json['challenge']

    js_code = open(f'{os.path.dirname(os.path.abspath(__file__))}/w.js', 'r', encoding='utf-8').read()
    js_compile = execjs.compile(js_code)
    w_params = js_compile.call('get_w', position_str, gt, challenge, pic_url, c_, s, position, code1, code2)

    return w_params['w']


def ajax2(get2_json, register_json, session: Session):
    pic_type = get2_json['data']['pic_type']
    pic = 'https://static.geetest.com' + get2_json['data']['pic']
    pic_content = get_pic(pic, register_json, session)

    gct_url = 'https://static.geetest.com' + get2_json['data']['gct_path']
    code1, code2 = get_gct_js_code(gct_url, session)

    if pic_type == 'nine':
        a = time.time()
        position_str, position = verify(pic_content)
        b = time.time()
        w = get_w(position_str, get2_json, register_json, position, code1, code2)

        logger.info(f'timing: {b - a}')
        if b - a < 1.8:
            t = 1.8 - (b - a)
            logger.info(f'sleep: {t}')
            time.sleep(t)

        url = 'https://api.geevisit.com/ajax.php'
        params = {
            "gt": register_json['gt'],
            "challenge": register_json['challenge'],
            "lang": "zh-cn",
            "pt": "0",
            "client_type": "web",
            "w": w,
            "callback": f"geetest_{int(time.time() * 1000)}"
        }
        response = request(session, 'GET', url, headers=headers, params=params, name='ajax2')

        res = json.loads(re.search(r'geetest_\d{13}\((.*)\)', response).group(1))
        logger.info(res)
        # if 'data' in res and res['data']['result'] == 'fail':
        #     with open(f'{os.path.dirname(os.path.abspath(__file__))}/error_pic/{md5(pic_content).hexdigest()}_{"_".join(str(i) for i in position)}.jpg',
        #               'wb') as f:
        #         f.write(pic_content)
        return res
    else:
        logger.warning('其他验证码类型')


def main(a):
    session = requests.session()
    session.mount('http://', HTTPAdapter(max_retries=2))
    session.mount('https://', HTTPAdapter(max_retries=2))
    session.proxies = {'http': 'http://10.99.138.95:30636', 'https': 'http://10.99.138.95:30636'}

    gettype(a, session)
    get1(a, session)
    type_ = ajax1(a, session)

    d = get2(a, type_, session)
    result = ajax2(d, a, session)
    if result['data']['result'] == 'fail':
        logger.warning('验证失败')
        return None, None
    return result['data']['validate'], a['challenge']


if __name__ == '__main__':
    for _ in range(10):
        main(register())
        # time.sleep(1)
