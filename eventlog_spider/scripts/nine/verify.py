import requests
from loguru import logger


def click_verify(image):
    try:
        res = requests.post('http://10.99.199.117:1401/nine_cls', files=[('files', image)], timeout=5)
        return res.json()
    except requests.exceptions.Timeout:
        logger.warning("\n-------------------------------------------------------------------------------\n"
                       "\n"
                       "验证码识别超时5s\n"
                       "\n"
                       "-------------------------------------------------------------------------------")
        raise Exception("验证码识别超时5s")


if __name__ == '__main__':
    pass
