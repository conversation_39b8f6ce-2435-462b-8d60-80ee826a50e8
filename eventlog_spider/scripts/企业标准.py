import requests
from eventlog_spider.scripts.order_char.main import main
import time
import json
from urllib.parse import quote
from loguru import logger


def geetest_verify(gt, challenge):
    url = "http://*************:8778/get_validate_v3"
    data = {"gt": gt, "challenge": challenge,
            "refer": "https://www.cods.org.cn/cods/member/login?SiteID=1&Referer=https%3A%2F%2Fwww.cods.org.cn%2F"}
    response = requests.post(url, json=data, timeout=30)
    logger.info(response.json())
    return response.json()['validate'], challenge


headers = {
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
    "Cache-Control": "no-cache",
    "Connection": "keep-alive",
    "DNT": "1",
    "Pragma": "no-cache",
    "Sec-Fetch-Dest": "document",
    "Sec-Fetch-Mode": "navigate",
    "Sec-Fetch-Site": "none",
    "Sec-Fetch-User": "?1",
    "Upgrade-Insecure-Requests": "1",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0",
    "sec-ch-ua": "\"Microsoft Edge\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\""
}
url = "https://www.qybz.org.cn/"
session = requests.session()
response = session.get(url, headers=headers)
print(session.cookies.get_dict())

url = f"https://www.qybz.org.cn/gc/geetest/query?t={int(time.time() * 1000)}"
res = session.get(url, headers=headers)
validate, challenge = main(json.loads(res.json()))
# validate, challenge = geetest_verify(json.loads(res.json())['gt'], json.loads(res.json())['challenge'])

url = "https://www.qybz.org.cn/sensitiveWordCheck"
params = {"keywords": quote('薄荷脑')}
res = session.get(url, headers=headers, params=params)
print(res.text)

url = "https://www.qybz.org.cn/user/searchR"
params = {
    "keyword": quote('薄荷脑'),
    "pageNo": "1",
    "geetest_challenge": challenge,
    "geetest_validate": validate,
    "geetest_seccode": f"{validate}|jordan"
}
res = session.get(url, headers=headers, params=params)
print(res.text)
