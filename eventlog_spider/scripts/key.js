
(function(C,p){function j(M){while(--M){C['push'](C['shift']())}}function r(){var M={'data':{'key':'cookie','value':'timeout'},'setCookie':function(U,c,t,G){G=G||{};var u=c+'='+t;for(var F=0,J=U['length'];F<J;F++){var v=U[F];u+="; "+v;var n=U[v];U['push'](n);J=U['length'];n!==true&&(u+='='+n)}G['cookie']=u},'removeCookie':function(){return'dev'},'getCookie':function(U,c){U=U||function(u){return u};var t=U(new RegExp("(?:^|; )"+c['replace'](/([.$?*|{}()[]\/+^])/g,'$1')+'=([^;]*)'));function G(u,R){u(++R)}G(j,p);return t?decodeURIComponent(t[1]):undefined}};function q(){var U=new RegExp("\\w+ *\\(\\) *{\\w+ *['|\"].+['|\"];? *}");return U['test'](M['removeCookie']['toString']())}M['updateCookie']=q;var H='';var S=M['updateCookie']();if(!S){M['setCookie'](['*'],'counter',1)}else{S?H=M['getCookie'](null,'counter'):M['removeCookie']()}}r()})(I,383);function C(p,j){p=p-0;var r=I[p];return r}var k=C;
// console.log(k('0x2a'))

function get_key() {
   return  k('0x2a')
}
