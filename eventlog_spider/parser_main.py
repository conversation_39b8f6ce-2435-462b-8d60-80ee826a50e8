# encoding=utf8
import argparse
import json
from concurrent.futures import Future
from threading import Lock
from resx.log import setup_logger
from pydantic import ValidationError

ap = argparse.ArgumentParser(description='通用爬虫-解析程序 解析数据到MSV 并发送Kafka消息')
ap.add_argument('--worker-num', type=int, default=1, help='worker数量')
ap_args = ap.parse_args()
logger = setup_logger(use_crawler_log=True, name='eventlog-parser', debug=False)
from resx.func import get_stack_info, cur_ts_sec
from resx.tools import BoundedExecutor
from resx.kafka_client import KafkaProducerClient
from resx.config import *
from resx.redis_types import RedisQueue
from resx.func import get_env
from eventlog_spider.common.eventlog import Eventlog, SpiderCode, EventlogOld, EventlogNew
from eventlog_spider.parser.parser import Parser
from eventlog_spider.common.eventlog_unify import StatusCode
from eventlog_spider.parser.parser_gs_jx import GSJXParser
from eventlog_spider.parser.parser_gs_js import GSJSParser
from eventlog_spider.parser.parser_gs_heilongjiang import GSHeiLongJiangParser
from eventlog_spider.parser.parser_gs_hainan import GSHaiNanParser
from eventlog_spider.parser.parser_gs_tianjing import TJParser
from eventlog_spider.parser.parser_gs_zhuhai import ZhuhaiParser
from eventlog_spider.parser.parser_gs_xiamen import XiamenParser
from eventlog_spider.parser.parser_gs_shanghai import ShanghaiParser
from eventlog_spider.parser.parser_gs_gdgz import GDGZParser
from eventlog_spider.parser.parser_gs_beijing import BeiJingParser
from eventlog_spider.parser.parser_gs_sichuan_chengdu import ChengduParser
from eventlog_spider.parser.parser_gs_guangdong import GSGuangDongParser
from eventlog_spider.parser.parser_gs_guizhou import GSGuiZhouParser
from eventlog_spider.parser.parser_acftu import ACFTUParser
from eventlog_spider.parser.parser_npo import ParserNpo
from eventlog_spider.parser.parser_law import ParserLaw
from eventlog_spider.parser.parser_cods import CodsParser
from eventlog_spider.parser.parser_hk import ParserHK
from eventlog_spider.parser.parser_gov_unit import GovUnitParser
from eventlog_spider.parser.parser_foundation import FoundationParser
from eventlog_spider.parser.parser_gds import GdsParser

writer_eventlog_old = KafkaProducerClient(kafka_topic='gsxt.data_fusion', bootstrap_servers=CFG_KAFKA_HUAWEI)
writer_eventlog_new = KafkaProducerClient(kafka_topic='octopvs_feedback_queue', bootstrap_servers=CFG_KAFKA_HUAWEI)

fs = {}
fs_lock = Lock()


def callback_fn(future: Future):
    eventlog: Eventlog = fs[future]
    with fs_lock:
        del fs[future]
    try:
        eventlog = future.result()
    except Exception as e:
        if hasattr(eventlog, 'spider_code'):
            eventlog.spider_code = SpiderCode.GIVE_UP
        else:
            eventlog.code = StatusCode.GIVE_UP
        logger.info(f'error process {eventlog} set SpiderCode.GIVE_UP {e} {get_stack_info()}')
    if hasattr(eventlog, 'spider'):
        eventlog.spider.send_ts = cur_ts_sec()
    eventlog_str = eventlog.to_json()
    ret = writer_eventlog_old.write(eventlog_str) if isinstance(eventlog, EventlogOld) else writer_eventlog_new.write(eventlog_str)
    logger.info(f'OUTPUT {eventlog_str} ret={ret}')


def eventlog(dd: dict):
    try:
        return EventlogOld(**dd)
    except ValidationError as e:
        return EventlogNew(**dd)


if __name__ == '__main__':
    crawler_output_queue = RedisQueue(**CFG_REDIS_GS, name=f'eventlog_spider:crawler_output.{get_env().value}', db=3)
    # spider_name -> parser_cls 获取所有的解析类
    parsers = {
        GdsParser.get_name(): GdsParser(),
        ACFTUParser.get_name(): ACFTUParser(),
        ParserNpo.get_name(): ParserNpo(),
        ParserLaw.get_name(): ParserLaw(),
        GSJXParser.get_name(): GSJXParser(),
        GSJSParser.get_name(): GSJSParser(),
        GSHeiLongJiangParser.get_name(): GSHeiLongJiangParser(),
        GSHaiNanParser.get_name(): GSHaiNanParser(),
        CodsParser.get_name(): CodsParser(),
        FoundationParser.get_name(): FoundationParser(),
        ParserHK.get_name(): ParserHK(),
        GovUnitParser.get_name(): GovUnitParser(),
        TJParser.get_name(): TJParser(),
        ZhuhaiParser.get_name(): ZhuhaiParser(),
        XiamenParser.get_name(): XiamenParser(),
        ShanghaiParser.get_name(): ShanghaiParser(),
        GDGZParser.get_name(): GDGZParser(),
        BeiJingParser.get_name(): BeiJingParser(),
        ChengduParser.get_name(): ChengduParser(),
        GSGuangDongParser.get_name(): GSGuangDongParser(),
        GSGuiZhouParser.get_name(): GSGuiZhouParser()
    }
    logger.info(f'successful load parsers {len(parsers)}')
    for k, v in sorted(parsers.items()):
        logger.info(f'crawler: {k} --> {type(v)}')

    # 启动线程池
    with BoundedExecutor(max_workers=ap_args.worker_num, thread_name_prefix='parser') as process_pool:
        for s in crawler_output_queue.generate():
            try:
                d = json.loads(s)
                eventlog_: Eventlog = eventlog(d)
                if not eventlog_:
                    logger.warning(f'error from dict {s}')
                    continue
            except Exception as e_:
                logger.warning(f'error eventlog {e_}')
                continue
            parser: Parser = parsers[eventlog_.selector.inst_name]
            future_ = process_pool.submit(parser.parse, eventlog_)
            fs[future_] = eventlog_
            future_.add_done_callback(callback_fn)
