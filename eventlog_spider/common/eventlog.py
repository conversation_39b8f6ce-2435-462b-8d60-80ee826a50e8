from typing import TypeVar
from eventlog_spider.common.eventlog_new import Eventlog as EventlogNew
from eventlog_spider.common.eventlog_old import Eventlog as EventlogOld, SpiderCode
from eventlog_spider.common.eventlog_unify import Eventlog as UnifyEventlog

Eventlog = TypeVar('Eventlog', EventlogOld, EventlogNew, UnifyEventlog)

if __name__ == '__main__':
    e = Eventlog.from_dict({'event_id': 1, 'spider_code': SpiderCode.UNFILLED})
    print(e)
