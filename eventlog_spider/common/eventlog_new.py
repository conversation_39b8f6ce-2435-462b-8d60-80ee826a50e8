# encoding=utf8
import json
from pydantic import Field
from typing import Optional
from resx.base_model import BaseModel
from eventlog_spider.common.eventlog_old import SpiderCode
from resx.log import setup_logger

logger = setup_logger(name=__name__)


class SelectorLog(BaseModel):
    send_ts: int = Field(default=-1)
    receive_ts: int = Field(default=-1)
    reason: str
    clue: bool
    entry_name: str
    inst_name: str
    word: str
    info: dict = Field(default={})
    try_id: int = Field(default=0)
    meta: dict = Field(default={})
    weight: int = Field(default=-1)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def get_info(self, key) -> Optional:
        if self.info is None:
            return None
        return self.info.get(key, None)


class SpiderLog(BaseModel):
    receive_ts: int = Field(default=-1)
    send_ts: int = Field(default=-1)
    spider_data: dict = Field(default={})
    ab_info: dict = Field(default=dict())

    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class Eventlog(BaseModel):
    event_id: str
    spider_code: SpiderCode = Field(default=SpiderCode.UNFILLED, alias='code')
    selector: SelectorLog
    spider: SpiderLog

    def __init__(self, **kwargs):
        super().__init__(**kwargs)


if __name__ == '__main__':
    from resx.redis_types import RedisQueue
    from resx.config import CFG_REDIS_GS

    input_queue = RedisQueue(name='cods', **CFG_REDIS_GS, db=9)
    a = json.loads(input_queue.pop())
    eventlog = Eventlog.from_dict(a)
    print(eventlog)
